# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

### Build and Run
```bash
# Build (development)
cargo build

# Build (release)
cargo build --release

# Run bot
cargo run

# Run in dry-run mode (safe testing)
cargo run -- --dry-run
```

### Testing and Quality
```bash
# Run tests
cargo test

# Format code
cargo fmt

# Run linting
cargo clippy
```

### Docker
```bash
# Build and run with Docker Compose
docker-compose up -d

# View logs
docker-compose logs -f sniperbot
```

## Architecture Overview

SniperBot is a Solana trading bot with a modular strategy-based architecture:

### Core Components

- **`main.rs`** - Entry point that orchestrates bot initialization, strategy selection, and concurrent API server
- **`core/sniper_bot.rs`** - Main bot engine handling wallet management, RPC connections, and trading loop execution
- **`strategies/`** - Pluggable trading strategies implementing the `Strategy` trait:
  - `MicroBotStrategy` - Aggressive meme token trading with 0.4 SOL capital
  - `MeteoraDammStrategy` - Early liquidity provision on new pools
- **`memory/`** - Local SQLite-based storage system for trade history and learning
- **`config/`** - Configuration management from `config.toml` and environment variables
- **`api/`** - REST API and WebSocket server for monitoring and control

### Strategy System

Strategies implement the async `Strategy` trait:
```rust
async fn execute() -> Result<()>        // Main trading logic
fn name() -> &str                       // Strategy identifier
async fn should_continue() -> bool      // Continue condition
async fn get_metrics() -> StrategyMetrics  // Performance data
```

Each strategy uses `BaseStrategy` for common functionality like trade logging and historical analysis.

### Configuration

- **Strategy selection**: Set `strategy = "microbot"` or `"meteora"` in `config.toml`
- **Wallet management**: Supports environment variable (`SOLANA_PRIVATE_KEY`) or JSON file
- **Safety**: Dry-run mode enabled by default (`dry_run = true`)
- **Network**: Multiple RPC endpoints with failover support

### Key Dependencies

- Solana SDK ecosystem (solana-sdk, solana-client, anchor-client)
- Jupiter integration for token swaps
- Axum web framework for API server
- SQLx for local memory storage
- Tokio async runtime

### Trading Flow

1. Bot initializes with selected strategy and memory system
2. API server starts in background for monitoring
3. Main trading loop executes strategy iterations
4. Each iteration scans for opportunities, executes trades, and logs results
5. Metrics are periodically reported and stored in local memory

The bot runs continuously until stopped via API endpoint, Ctrl+C, or strategy completion condition.