[package]
name = "sniperbot"
version = "0.1.0"
edition = "2021"

[dependencies]
# Solana
solana-sdk = "1.18"
solana-client = "1.18"
solana-transaction-status = "1.18"
solana-account-decoder = "1.18"
anchor-client = "0.29"
spl-token = "4.0"
spl-associated-token-account = "2.3"

# Async runtime
tokio = { version = "1.35", features = ["full"] }
async-trait = "0.1"

# Web framework
axum = { version = "0.7", features = ["ws"] }
tower = "0.4"
tower-http = { version = "0.5", features = ["cors", "trace"] }

# Serialization
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
bincode = "1.3"

# Utils
anyhow = "1.0"
thiserror = "1.0"
chrono = { version = "0.4", features = ["serde"] }
uuid = { version = "1.6", features = ["v4", "serde"] }

# Logging
tracing = "0.1"
tracing-subscriber = { version = "0.3", features = ["env-filter", "fmt", "json"] }

# HTTP client
reqwest = { version = "0.11", features = ["json", "rustls-tls"] }

# Database (for OpenMemory-like functionality)
sqlx = { version = "0.6", features = ["runtime-tokio-rustls", "sqlite", "chrono", "uuid"] }

# Config
config = "0.14"
dotenv = "0.15"

# Crypto
bs58 = "0.5"
base64 = "0.22"

# Jupiter integration
jupiter-swap-api-client = "0.1"

# Math
rust_decimal = "1.33"
rust_decimal_macros = "1.33"

# Random (for simulation)
rand = "0.8"

# Additional parsing
toml = "0.8"

# AI/ML (lightweight)
smartcore = { version = "0.3", optional = true }
linfa = { version = "0.7", optional = true }

[dev-dependencies]
criterion = "0.5"

[features]
default = []
ml = ["smartcore", "linfa"]

[[bin]]
name = "sniperbot"
path = "src/main.rs"

[profile.release]
opt-level = 3
lto = "fat"
codegen-units = 1
panic = "abort"
strip = true
