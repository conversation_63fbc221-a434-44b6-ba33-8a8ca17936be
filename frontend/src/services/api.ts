import axios from 'axios';
import { BotStatus, Metrics, Trade, Position, Settings } from '../types';

const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:8080';

const api = axios.create({
  baseURL: API_BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Add request interceptor for auth
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('auth_token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => Promise.reject(error)
);

// Add response interceptor for error handling
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      // Handle unauthorized
      localStorage.removeItem('auth_token');
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

export const botApi = {
  // Health & Status
  getHealth: () => api.get<string>('/health'),
  getStatus: () => api.get<BotStatus>('/status'),
  getMetrics: () => api.get<Metrics>('/metrics'),
  
  // Bot Control
  stopBot: () => api.post('/stop'),
  startBot: () => api.post('/start'),
  restartBot: () => api.post('/restart'),
  
  // Trading
  getTrades: (params?: { limit?: number; offset?: number }) => 
    api.get<Trade[]>('/trades', { params }),
  getActivePositions: () => api.get<Position[]>('/positions/active'),
  closePosition: (id: string) => api.delete(`/positions/${id}`),
  executeTrade: (params: {
    token_address: string;
    action: 'buy' | 'sell';
    amount: number;
  }) => api.post('/trades/execute', params),
  
  // Strategy
  getStrategies: () => api.get<string[]>('/strategies'),
  getActiveStrategy: () => api.get<{ name: string; config: any }>('/strategies/active'),
  activateStrategy: (name: string) => api.post(`/strategies/${name}/activate`),
  updateStrategyConfig: (name: string, config: any) => 
    api.put(`/strategies/${name}/config`, config),
  
  // Settings
  getSettings: () => api.get<Settings>('/settings'),
  updateSettings: (settings: Partial<Settings>) => api.put('/settings', settings),
  
  // Analytics
  getPnLHistory: (period: '1d' | '7d' | '30d' | 'all') => 
    api.get(`/analytics/pnl?period=${period}`),
  getPerformanceMetrics: () => api.get('/analytics/performance'),
  getTokenAnalysis: (address: string) => api.get(`/analytics/tokens/${address}`),
};

export default api;