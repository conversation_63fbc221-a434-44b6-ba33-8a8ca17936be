import { io, Socket } from 'socket.io-client';
import { WSMessage } from '../types';

class WebSocketService {
  private socket: Socket | null = null;
  private listeners: Map<string, Set<(data: any) => void>> = new Map();

  connect(url: string = 'ws://localhost:8080') {
    if (this.socket?.connected) {
      return;
    }

    this.socket = io(url, {
      transports: ['websocket'],
      reconnection: true,
      reconnectionAttempts: 5,
      reconnectionDelay: 1000,
    });

    this.socket.on('connect', () => {
      console.log('WebSocket connected');
      this.emit('connected', { connected: true });
    });

    this.socket.on('disconnect', () => {
      console.log('WebSocket disconnected');
      this.emit('connected', { connected: false });
    });

    this.socket.on('error', (error) => {
      console.error('WebSocket error:', error);
      this.emit('error', error);
    });

    // Listen for messages
    this.socket.on('message', (message: WSMessage) => {
      this.emit(message.type, message.data);
    });

    // Specific event listeners
    this.socket.on('trade:new', (data) => this.emit('trade:new', data));
    this.socket.on('position:update', (data) => this.emit('position:update', data));
    this.socket.on('metrics:update', (data) => this.emit('metrics:update', data));
    this.socket.on('price:update', (data) => this.emit('price:update', data));
  }

  disconnect() {
    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
    }
  }

  subscribe(event: string, callback: (data: any) => void) {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, new Set());
    }
    this.listeners.get(event)!.add(callback);

    // Return unsubscribe function
    return () => {
      this.listeners.get(event)?.delete(callback);
    };
  }

  private emit(event: string, data: any) {
    this.listeners.get(event)?.forEach(callback => callback(data));
  }

  // Send events to server
  subscribeToToken(address: string) {
    this.socket?.emit('subscribe:token', { address });
  }

  unsubscribeFromToken(address: string) {
    this.socket?.emit('unsubscribe:token', { address });
  }

  sendMessage(type: string, data: any) {
    this.socket?.emit(type, data);
  }

  isConnected(): boolean {
    return this.socket?.connected || false;
  }
}

export const wsService = new WebSocketService();
export default wsService;