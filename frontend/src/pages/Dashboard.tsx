import React from 'react';
import { Row, Col, Card, Table, Tag, Space, Typography } from 'antd';
import { useQuery } from '@tanstack/react-query';
import { MetricsCard } from '../components/Dashboard/MetricsCard';
import { TradingChart } from '../components/Dashboard/TradingChart';
import { useStore } from '../store/useStore';
import { botApi } from '../services/api';
import { Trade, Position } from '../types';

const { Title, Text } = Typography;

export const Dashboard: React.FC = () => {
  const { metrics, positions, trades } = useStore();

  // Fetch data
  const { data: statusData } = useQuery({
    queryKey: ['status'],
    queryFn: async () => {
      const response = await botApi.getStatus();
      return response.data;
    },
    refetchInterval: 5000,
  });

  const { data: metricsData } = useQuery({
    queryKey: ['metrics'],
    queryFn: async () => {
      const response = await botApi.getMetrics();
      return response.data;
    },
    refetchInterval: 5000,
  });

  // Mock chart data
  const chartData = React.useMemo(() => {
    const now = Date.now();
    return Array.from({ length: 50 }, (_, i) => ({
      time: now - (50 - i) * 60000,
      price: 0.0001 + Math.random() * 0.0002,
      volume: Math.random() * 10000,
    }));
  }, []);

  const tradeColumns = [
    {
      title: 'Time',
      dataIndex: 'timestamp',
      key: 'timestamp',
      width: 100,
      render: (timestamp: string) => new Date(timestamp).toLocaleTimeString(),
    },
    {
      title: 'Token',
      dataIndex: 'token_symbol',
      key: 'token_symbol',
      render: (symbol: string) => <Tag color="blue">{symbol}</Tag>,
    },
    {
      title: 'Action',
      dataIndex: 'action',
      key: 'action',
      render: (action: string) => (
        <Tag color={action === 'Buy' ? 'green' : 'red'}>{action}</Tag>
      ),
    },
    {
      title: 'Amount',
      dataIndex: 'amount_sol',
      key: 'amount_sol',
      render: (amount: number) => `${amount.toFixed(4)} SOL`,
    },
    {
      title: 'P&L',
      dataIndex: 'pnl_sol',
      key: 'pnl_sol',
      render: (pnl?: number) => {
        if (!pnl) return '-';
        return (
          <Text className={pnl > 0 ? 'text-green-500' : 'text-red-500'}>
            {pnl > 0 ? '+' : ''}{pnl.toFixed(4)} SOL
          </Text>
        );
      },
    },
  ];

  const positionColumns = [
    {
      title: 'Token',
      dataIndex: 'token_symbol',
      key: 'token_symbol',
      render: (symbol: string) => <Tag color="blue">{symbol}</Tag>,
    },
    {
      title: 'Entry Price',
      dataIndex: 'entry_price',
      key: 'entry_price',
      render: (price: number) => price.toFixed(8),
    },
    {
      title: 'Current Price',
      dataIndex: 'current_price',
      key: 'current_price',
      render: (price: number) => price.toFixed(8),
    },
    {
      title: 'P&L',
      key: 'pnl',
      render: (_: any, record: Position) => {
        const pnlPercent = record.pnl_percent;
        return (
          <Space>
            <Text className={pnlPercent > 0 ? 'text-green-500' : 'text-red-500'}>
              {pnlPercent > 0 ? '+' : ''}{pnlPercent.toFixed(2)}%
            </Text>
            <Text className="text-gray-400">
              ({record.pnl_sol.toFixed(4)} SOL)
            </Text>
          </Space>
        );
      },
    },
  ];

  return (
    <div className="space-y-6">
      <Title level={2} className="text-white">Dashboard</Title>
      
      {/* Metrics Row */}
      <Row gutter={[16, 16]}>
        <Col xs={24} sm={12} md={6}>
          <MetricsCard
            title="Balance"
            value={metricsData?.current_balance_sol || 0}
            suffix="SOL"
            precision={4}
            loading={!metricsData}
          />
        </Col>
        <Col xs={24} sm={12} md={6}>
          <MetricsCard
            title="Total P&L"
            value={metricsData?.total_pnl_sol || 0}
            suffix="SOL"
            precision={4}
            trend={{
              value: 12.5,
              isPositive: (metricsData?.total_pnl_sol || 0) > 0,
            }}
            loading={!metricsData}
          />
        </Col>
        <Col xs={24} sm={12} md={6}>
          <MetricsCard
            title="Win Rate"
            value={metricsData?.win_rate || 0}
            suffix="%"
            precision={1}
            loading={!metricsData}
          />
        </Col>
        <Col xs={24} sm={12} md={6}>
          <MetricsCard
            title="Total Trades"
            value={metricsData?.total_trades || 0}
            loading={!metricsData}
          />
        </Col>
      </Row>

      {/* Charts Row */}
      <Row gutter={[16, 16]}>
        <Col xs={24} lg={16}>
          <TradingChart data={chartData} title="Price Movement" />
        </Col>
        <Col xs={24} lg={8}>
          <Card
            title="Active Positions"
            className="bg-gray-800 border-gray-700"
            headStyle={{ borderBottom: '1px solid #374151' }}
          >
            <Table
              dataSource={positions.slice(0, 5)}
              columns={positionColumns}
              pagination={false}
              size="small"
              rowKey="id"
            />
          </Card>
        </Col>
      </Row>

      {/* Recent Trades */}
      <Card
        title="Recent Trades"
        className="bg-gray-800 border-gray-700"
        headStyle={{ borderBottom: '1px solid #374151' }}
      >
        <Table
          dataSource={trades.slice(0, 10)}
          columns={tradeColumns}
          pagination={false}
          size="small"
          rowKey="id"
        />
      </Card>
    </div>
  );
};