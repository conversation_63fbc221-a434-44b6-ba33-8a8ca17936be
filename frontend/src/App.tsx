import React from 'react';
import { BrowserRouter, Routes, Route } from 'react-router-dom';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { ConfigProvider, theme } from 'antd';
import { Layout } from './components/Layout';
import { Dashboard } from './pages/Dashboard';
import wsService from './services/websocket';
import { useStore } from './store/useStore';

// Create query client
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      refetchOnWindowFocus: false,
      retry: 1,
    },
  },
});

// Ant Design dark theme
const darkTheme = {
  algorithm: theme.darkAlgorithm,
  token: {
    colorPrimary: '#10b981',
    colorBgContainer: '#1f2937',
    colorBgElevated: '#374151',
    colorBorder: '#4b5563',
    colorText: '#f3f4f6',
    colorTextSecondary: '#9ca3af',
  },
};

function App() {
  const { setConnected, addTrade, updatePosition, setMetrics } = useStore();

  React.useEffect(() => {
    // Connect to WebSocket
    wsService.connect();

    // Setup listeners
    const unsubscribers = [
      wsService.subscribe('connected', ({ connected }) => setConnected(connected)),
      wsService.subscribe('trade:new', (trade) => addTrade(trade)),
      wsService.subscribe('position:update', (position) => updatePosition(position)),
      wsService.subscribe('metrics:update', (metrics) => setMetrics(metrics)),
    ];

    return () => {
      unsubscribers.forEach(unsub => unsub());
      wsService.disconnect();
    };
  }, []);

  return (
    <ConfigProvider theme={darkTheme}>
      <QueryClientProvider client={queryClient}>
        <BrowserRouter>
          <Routes>
            <Route path="/" element={<Layout />}>
              <Route index element={<Dashboard />} />
              <Route path="trading" element={<div className="text-white">Trading Page</div>} />
              <Route path="positions" element={<div className="text-white">Positions Page</div>} />
              <Route path="analytics" element={<div className="text-white">Analytics Page</div>} />
              <Route path="history" element={<div className="text-white">History Page</div>} />
              <Route path="strategies" element={<div className="text-white">Strategies Page</div>} />
              <Route path="settings" element={<div className="text-white">Settings Page</div>} />
            </Route>
          </Routes>
        </BrowserRouter>
      </QueryClientProvider>
    </ConfigProvider>
  );
}

export default App;