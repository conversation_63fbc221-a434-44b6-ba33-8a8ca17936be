import React from 'react';
import { Layout, Menu } from 'antd';
import { useNavigate, useLocation } from 'react-router-dom';
import {
  DashboardOutlined,
  LineChartOutlined,
  SwapOutlined,
  BarChartOutlined,
  SettingOutlined,
  HistoryOutlined,
  RocketOutlined,
} from '@ant-design/icons';

const { Sider } = Layout;

const menuItems = [
  {
    key: '/',
    icon: <DashboardOutlined />,
    label: 'Dashboard',
  },
  {
    key: '/trading',
    icon: <SwapOutlined />,
    label: 'Trading',
  },
  {
    key: '/positions',
    icon: <LineChartOutlined />,
    label: 'Positions',
  },
  {
    key: '/analytics',
    icon: <BarChartOutlined />,
    label: 'Analytics',
  },
  {
    key: '/history',
    icon: <HistoryOutlined />,
    label: 'History',
  },
  {
    key: '/strategies',
    icon: <RocketOutlined />,
    label: 'Strategies',
  },
  {
    key: '/settings',
    icon: <SettingOutlined />,
    label: 'Settings',
  },
];

export const Sidebar: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const [collapsed, setCollapsed] = React.useState(false);

  return (
    <Sider
      collapsible
      collapsed={collapsed}
      onCollapse={setCollapsed}
      className="bg-gray-900 border-r border-gray-800"
    >
      <Menu
        theme="dark"
        selectedKeys={[location.pathname]}
        mode="inline"
        items={menuItems}
        onClick={({ key }) => navigate(key)}
        className="bg-gray-900"
      />
    </Sider>
  );
};