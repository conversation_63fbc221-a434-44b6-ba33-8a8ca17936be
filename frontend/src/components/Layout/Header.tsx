import React from 'react';
import { Layout, Space, Badge, Button, Typography, Tooltip } from 'antd';
import { 
  PoweroffOutlined, 
  WifiOutlined, 
  DisconnectOutlined,
  ReloadOutlined 
} from '@ant-design/icons';
import { useStore } from '../../store/useStore';
import { botApi } from '../../services/api';

const { Header: AntHeader } = Layout;
const { Text } = Typography;

export const Header: React.FC = () => {
  const { status, isConnected, metrics } = useStore();
  const [loading, setLoading] = React.useState(false);

  const handleStop = async () => {
    try {
      setLoading(true);
      await botApi.stopBot();
    } catch (error) {
      console.error('Failed to stop bot:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleRestart = async () => {
    try {
      setLoading(true);
      await botApi.restartBot();
    } catch (error) {
      console.error('Failed to restart bot:', error);
    } finally {
      setLoading(false);
    }
  };

  const formatUptime = (seconds: number): string => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    return `${hours}h ${minutes}m`;
  };

  return (
    <AntHeader className="bg-gray-900 px-6 flex items-center justify-between border-b border-gray-800">
      <div className="flex items-center space-x-6">
        <div className="flex items-center space-x-2">
          <img src="/logo.svg" alt="SniperBot" className="h-8 w-8" />
          <Text className="text-xl font-bold text-white">SniperBot</Text>
        </div>
        
        <Badge 
          status={status?.bot_status === 'active' ? 'success' : 'error'} 
          text={
            <Text className="text-gray-300">
              {status?.bot_status?.toUpperCase() || 'OFFLINE'}
            </Text>
          }
        />
        
        {status?.dry_run_mode && (
          <Badge 
            status="warning" 
            text={<Text className="text-yellow-400">DRY RUN</Text>}
          />
        )}
      </div>

      <Space size="middle">
        {metrics && (
          <Space className="text-gray-300">
            <Tooltip title="Uptime">
              <Text>{formatUptime(metrics.uptime_seconds)}</Text>
            </Tooltip>
            <Text>|</Text>
            <Tooltip title="Balance">
              <Text>{metrics.current_balance_sol.toFixed(4)} SOL</Text>
            </Tooltip>
            <Text>|</Text>
            <Tooltip title="Win Rate">
              <Text className={metrics.win_rate >= 50 ? 'text-green-400' : 'text-red-400'}>
                {metrics.win_rate.toFixed(1)}%
              </Text>
            </Tooltip>
          </Space>
        )}
        
        <Tooltip title={isConnected ? 'Connected' : 'Disconnected'}>
          <Badge status={isConnected ? 'success' : 'error'}>
            {isConnected ? <WifiOutlined /> : <DisconnectOutlined />}
          </Badge>
        </Tooltip>
        
        <Button 
          icon={<ReloadOutlined />} 
          onClick={handleRestart}
          loading={loading}
          disabled={status?.bot_status !== 'active'}
        >
          Restart
        </Button>
        
        <Button 
          danger 
          icon={<PoweroffOutlined />} 
          onClick={handleStop}
          loading={loading}
          disabled={status?.bot_status !== 'active'}
        >
          Stop
        </Button>
      </Space>
    </AntHeader>
  );
};