import React from 'react';
import { Card, Statistic, Typography } from 'antd';
import { ArrowUpOutlined, ArrowDownOutlined } from '@ant-design/icons';

const { Text } = Typography;

interface MetricsCardProps {
  title: string;
  value: string | number;
  prefix?: React.ReactNode;
  suffix?: string;
  trend?: {
    value: number;
    isPositive: boolean;
  };
  precision?: number;
  loading?: boolean;
  className?: string;
}

export const MetricsCard: React.FC<MetricsCardProps> = ({
  title,
  value,
  prefix,
  suffix,
  trend,
  precision,
  loading = false,
  className = '',
}) => {
  return (
    <Card 
      className={`bg-gray-800 border-gray-700 ${className}`}
      loading={loading}
    >
      <Statistic
        title={<Text className="text-gray-400">{title}</Text>}
        value={value}
        precision={precision}
        prefix={prefix}
        suffix={suffix}
        valueStyle={{ color: '#fff', fontSize: '24px' }}
      />
      {trend && (
        <div className="mt-2 flex items-center">
          {trend.isPositive ? (
            <ArrowUpOutlined className="text-green-500" />
          ) : (
            <ArrowDownOutlined className="text-red-500" />
          )}
          <Text className={trend.isPositive ? 'text-green-500' : 'text-red-500'}>
            {Math.abs(trend.value).toFixed(2)}%
          </Text>
        </div>
      )}
    </Card>
  );
};