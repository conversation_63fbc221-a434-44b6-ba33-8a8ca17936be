import React from 'react';
import { Card } from 'antd';
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  Area,
  AreaChart,
} from 'recharts';
import { PricePoint } from '../../types';

interface TradingChartProps {
  data: PricePoint[];
  title?: string;
  height?: number;
  showVolume?: boolean;
}

export const TradingChart: React.FC<TradingChartProps> = ({
  data,
  title = 'Price Chart',
  height = 400,
  showVolume = false,
}) => {
  const formatTime = (timestamp: number) => {
    const date = new Date(timestamp);
    return date.toLocaleTimeString('en-US', { 
      hour: '2-digit', 
      minute: '2-digit' 
    });
  };

  const formatPrice = (price: number) => {
    if (price < 0.01) return price.toExponential(2);
    return price.toFixed(4);
  };

  return (
    <Card 
      title={title}
      className="bg-gray-800 border-gray-700"
      headStyle={{ borderBottom: '1px solid #374151' }}
    >
      <ResponsiveContainer width="100%" height={height}>
        <AreaChart data={data}>
          <defs>
            <linearGradient id="colorPrice" x1="0" y1="0" x2="0" y2="1">
              <stop offset="5%" stopColor="#10b981" stopOpacity={0.8}/>
              <stop offset="95%" stopColor="#10b981" stopOpacity={0}/>
            </linearGradient>
            <linearGradient id="colorVolume" x1="0" y1="0" x2="0" y2="1">
              <stop offset="5%" stopColor="#6366f1" stopOpacity={0.8}/>
              <stop offset="95%" stopColor="#6366f1" stopOpacity={0}/>
            </linearGradient>
          </defs>
          <CartesianGrid strokeDasharray="3 3" stroke="#374151" />
          <XAxis 
            dataKey="time" 
            tickFormatter={formatTime}
            stroke="#9ca3af"
          />
          <YAxis 
            yAxisId="price"
            orientation="left"
            tickFormatter={formatPrice}
            stroke="#9ca3af"
          />
          {showVolume && (
            <YAxis 
              yAxisId="volume"
              orientation="right"
              stroke="#9ca3af"
            />
          )}
          <Tooltip
            contentStyle={{
              backgroundColor: '#1f2937',
              border: '1px solid #374151',
              borderRadius: '4px',
            }}
            labelFormatter={(value) => formatTime(value as number)}
            formatter={(value: number) => formatPrice(value)}
          />
          <Area
            yAxisId="price"
            type="monotone"
            dataKey="price"
            stroke="#10b981"
            fillOpacity={1}
            fill="url(#colorPrice)"
          />
          {showVolume && (
            <Area
              yAxisId="volume"
              type="monotone"
              dataKey="volume"
              stroke="#6366f1"
              fillOpacity={1}
              fill="url(#colorVolume)"
            />
          )}
        </AreaChart>
      </ResponsiveContainer>
    </Card>
  );
};