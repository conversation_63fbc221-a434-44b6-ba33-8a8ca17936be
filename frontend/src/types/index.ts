// API Types
export interface BotStatus {
  bot_status: 'active' | 'inactive' | 'error';
  current_strategy: string;
  wallet_address: string;
  rpc_endpoint: string;
  dry_run_mode: boolean;
}

export interface Metrics {
  status: string;
  total_trades: number;
  win_rate: number;
  total_pnl_sol: number;
  current_balance_sol: number;
  uptime_seconds: number;
}

export interface Trade {
  id: string;
  timestamp: string;
  token_address: string;
  token_symbol: string;
  action: 'Buy' | 'Sell' | 'AddLiquidity' | 'RemoveLiquidity';
  amount_sol: number;
  price: number;
  tx_signature: string;
  pnl_sol?: number;
  notes?: string;
}

export interface Position {
  id: string;
  token_address: string;
  token_symbol: string;
  entry_price: number;
  current_price: number;
  amount: number;
  pnl_sol: number;
  pnl_percent: number;
  status: 'active' | 'closed';
}

export interface TokenInfo {
  address: string;
  symbol: string;
  name: string;
  price: number;
  liquidity_usd: number;
  volume_24h: number;
  holders: number;
  risk_score: number;
}

export interface Strategy {
  name: string;
  type: 'microbot' | 'meteora';
  config: Record<string, any>;
  active: boolean;
}

// WebSocket Events
export interface WSMessage {
  type: 'trade' | 'position' | 'metrics' | 'price' | 'error';
  data: any;
  timestamp: string;
}

// Chart Types
export interface PricePoint {
  time: number;
  price: number;
  volume?: number;
}

export interface ChartData {
  prices: PricePoint[];
  indicators: {
    sma20?: number[];
    sma50?: number[];
    volume?: number[];
  };
}

// Settings Types
export interface Settings {
  strategy: string;
  wallet: {
    private_key_path: string;
    use_env_key: boolean;
  };
  rpc: {
    endpoints: string[];
    helius_api_key?: string;
    websocket_url?: string;
  };
  trading: {
    slippage_bps: number;
    priority_fee_lamports: number;
    dry_run: boolean;
  };
  microbot: {
    initial_capital_sol: number;
    position_size_percent: number;
    stop_loss_percent: number;
    take_profit_targets: number[];
    max_token_age_minutes: number;
    min_liquidity_usd: number;
  };
  meteora: {
    min_pool_liquidity_usd: number;
    max_initial_fee_bps: number;
    position_size_usd: number;
    max_impermanent_loss_percent: number;
    compound_threshold_usd: number;
  };
}