import { create } from 'zustand';
import { devtools, persist } from 'zustand/middleware';
import { BotStatus, Metrics, Trade, Position, Settings } from '../types';

interface BotState {
  // Bot Status
  status: BotStatus | null;
  metrics: Metrics | null;
  isConnected: boolean;
  
  // Trading Data
  trades: Trade[];
  positions: Position[];
  selectedToken: string | null;
  
  // Settings
  settings: Settings | null;
  theme: 'light' | 'dark';
  
  // Actions
  setStatus: (status: BotStatus) => void;
  setMetrics: (metrics: Metrics) => void;
  setConnected: (connected: boolean) => void;
  setTrades: (trades: Trade[]) => void;
  addTrade: (trade: Trade) => void;
  setPositions: (positions: Position[]) => void;
  updatePosition: (position: Position) => void;
  setSelectedToken: (token: string | null) => void;
  setSettings: (settings: Settings) => void;
  setTheme: (theme: 'light' | 'dark') => void;
  reset: () => void;
}

const initialState = {
  status: null,
  metrics: null,
  isConnected: false,
  trades: [],
  positions: [],
  selectedToken: null,
  settings: null,
  theme: 'dark' as const,
};

export const useStore = create<BotState>()(
  devtools(
    persist(
      (set, get) => ({
        ...initialState,
        
        setStatus: (status) => set({ status }),
        setMetrics: (metrics) => set({ metrics }),
        setConnected: (isConnected) => set({ isConnected }),
        
        setTrades: (trades) => set({ trades }),
        addTrade: (trade) => set((state) => ({ 
          trades: [trade, ...state.trades].slice(0, 100) // Keep last 100 trades
        })),
        
        setPositions: (positions) => set({ positions }),
        updatePosition: (position) => set((state) => ({
          positions: state.positions.map(p => 
            p.id === position.id ? position : p
          )
        })),
        
        setSelectedToken: (selectedToken) => set({ selectedToken }),
        setSettings: (settings) => set({ settings }),
        setTheme: (theme) => set({ theme }),
        
        reset: () => set(initialState),
      }),
      {
        name: 'sniperbot-storage',
        partialize: (state) => ({ 
          theme: state.theme,
          selectedToken: state.selectedToken,
        }),
      }
    )
  )
);

// Selector hooks for better performance
export const useStatus = () => useStore((state) => state.status);
export const useMetrics = () => useStore((state) => state.metrics);
export const useIsConnected = () => useStore((state) => state.isConnected);
export const useTrades = () => useStore((state) => state.trades);
export const usePositions = () => useStore((state) => state.positions);
export const useSelectedToken = () => useStore((state) => state.selectedToken);
export const useSettings = () => useStore((state) => state.settings);
export const useTheme = () => useStore((state) => state.theme);