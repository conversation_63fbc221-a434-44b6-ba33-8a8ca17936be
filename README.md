# 🎯 SniperBot - Solana Trading Bot

Zaawansowany bot tradingowy na Solana z obsługą wielu strategii.

## ✨ Funkcje

- **MicroBot Strategy** - Agresywny trading małym kapitałem (0.4 SOL)
- **Meteora DAMM Strategy** - Wczesne dostarczanie płynności
- **Local Memory** - Uczenie się z historii (SQLite)
- **REST API** - Monitoring i kontrola
- **Docker Ready** - Łatwe wdrożenie

## 🚀 Szybki Start

### 1. Lokalne uruchomienie

```bash
# Sklonuj i skonfiguruj
cd sniperbot
cp .env.example .env
# Edytuj .env i dodaj swój klucz prywatny

# Buduj i uruchom
cargo build --release
cargo run

# Lub w trybie dry-run (bezpieczne testowanie)
cargo run -- --dry-run
```

### 2. Docker

```bash
# Uruchom z Docker Compose
docker-compose up -d

# Sprawdź logi
docker-compose logs -f sniperbot
```

### 3. Wybór strategii

Edytuj `config.toml`:
```toml
strategy = "microbot"  # lub "meteora"
```

## 📊 API Endpoints

- `GET /` - Status
- `GET /api/status` - Szczegółowy status
- `POST /api/stop` - Zatrzymaj bota
- `WS /ws` - WebSocket dla real-time updates

## ⚙️ Konfiguracja

Wszystkie ustawienia w `config.toml`:
- Kapitał początkowy
- Wielkość pozycji
- Stop loss / Take profit
- Parametry strategii

## 🛡️ Bezpieczeństwo

- **Dry Run Mode** - domyślnie włączony
- **Klucze w .env** - nigdy nie commituj
- **Docker isolation** - bezpieczne środowisko

## 📈 Strategie

### MicroBot (0.4 SOL)
- Skanuje nowe tokeny (< 5 min)
- Agresywne pozycje (80% kapitału)
- Szybkie zyski (50%, 100%, 200%)

### Meteora DAMM
- Wczesne LP w nowych pulach
- Zbiera opłaty z wysokiego wolumenu
- Auto-compound zysków

## 🔧 Rozwój

```bash
# Testy
cargo test

# Formatowanie
cargo fmt

# Linting
cargo clippy
```

## 📝 Licencja

MIT

---

**⚠️ UWAGA**: Trading kryptowalut jest ryzykowny. Używaj tylko środków, które możesz stracić!
