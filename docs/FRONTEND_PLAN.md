# SniperBot Frontend - Development Plan

## 🎯 Overview

Modern web dashboard for monitoring and controlling SniperBot with real-time updates and comprehensive analytics.

## 🛠️ Technology Stack

### Frontend
- **Framework**: React 18 with TypeScript
- **State Management**: Zustand or Redux Toolkit
- **UI Library**: Ant Design or Material-UI
- **Charts**: Recharts or TradingView Lightweight Charts
- **WebSocket**: Socket.io-client
- **Styling**: Tailwind CSS + styled-components
- **Build Tool**: Vite

### Backend Extensions
- Enhance existing Axum API
- Add WebSocket support for real-time data
- Implement authentication (JWT)

## 📐 Architecture

```
frontend/
├── src/
│   ├── components/       # Reusable UI components
│   ├── pages/           # Page components
│   ├── services/        # API and WebSocket services
│   ├── store/           # State management
│   ├── hooks/           # Custom React hooks
│   ├── utils/           # Utility functions
│   └── types/           # TypeScript definitions
├── public/
└── package.json
```

## 🎨 UI/UX Design

### 1. Dashboard (Main Page)
```
┌─────────────────────────────────────────────────────────┐
│ SniperBot Dashboard                    [Status: Active] │
├─────────────────────────────────────────────────────────┤
│ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐        │
│ │   Balance   │ │    P&L      │ │  Win Rate   │        │
│ │  0.4 SOL    │ │  +0.12 SOL  │ │    72%      │        │
│ └─────────────┘ └─────────────┘ └─────────────┘        │
│                                                          │
│ ┌─────────────────────────────┐ ┌─────────────────────┐│
│ │     Price Chart             │ │   Active Positions  ││
│ │     [Chart Area]            │ │   • BONK: +45%      ││
│ │                             │ │   • MYRO: -5%       ││
│ └─────────────────────────────┘ └─────────────────────┘│
│                                                          │
│ ┌─────────────────────────────────────────────────────┐│
│ │              Recent Trades                           ││
│ │  Time    Token   Action   Amount   P&L    Status    ││
│ │  14:32   BONK    BUY      0.32     +45%   ✓        ││
│ └─────────────────────────────────────────────────────┘│
└─────────────────────────────────────────────────────────┘
```

### 2. Strategy Configuration
- Strategy selector (MicroBot/Meteora)
- Parameter adjustment with sliders
- Risk management settings
- Save/Load configurations

### 3. Trading Terminal
- Token search and analysis
- Manual trade execution
- Order book visualization
- Risk assessment display

### 4. Analytics Page
- Performance charts
- Trade history with filters
- P&L analysis
- Strategy comparison

### 5. Settings
- Wallet management
- RPC configuration
- API keys management
- Notification preferences

## 🔌 API Integration

### REST Endpoints (Existing)
```typescript
GET  /api/health
GET  /api/metrics
GET  /api/status
POST /api/stop
```

### New Endpoints Needed
```typescript
// Authentication
POST /api/auth/login
POST /api/auth/refresh

// Trading
GET  /api/trades?limit=100&offset=0
GET  /api/positions/active
POST /api/trades/execute
DELETE /api/positions/:id

// Strategy
GET  /api/strategies
PUT  /api/strategies/:name/config
POST /api/strategies/:name/activate

// Analytics
GET  /api/analytics/pnl?period=7d
GET  /api/analytics/performance
GET  /api/analytics/tokens/:address

// Settings
GET  /api/settings
PUT  /api/settings
```

### WebSocket Events
```typescript
// Server -> Client
ws.on('trade:new', (trade) => {})
ws.on('position:update', (position) => {})
ws.on('metrics:update', (metrics) => {})
ws.on('price:update', (price) => {})

// Client -> Server
ws.emit('subscribe:token', { address })
ws.emit('unsubscribe:token', { address })
```

## 🧩 Key Components

### 1. MetricsCard
```typescript
interface MetricsCardProps {
  title: string;
  value: string | number;
  change?: number;
  icon?: ReactNode;
  color?: 'green' | 'red' | 'neutral';
}
```

### 2. TradingChart
```typescript
interface TradingChartProps {
  data: PriceData[];
  indicators?: Indicator[];
  onTimeframeChange?: (tf: Timeframe) => void;
}
```

### 3. PositionsList
```typescript
interface Position {
  id: string;
  token: TokenInfo;
  entryPrice: number;
  currentPrice: number;
  pnl: number;
  size: number;
}
```

### 4. TradeExecutor
```typescript
interface TradeExecutorProps {
  token: TokenInfo;
  onExecute: (params: TradeParams) => Promise<void>;
}
```

## 📱 Responsive Design

- Desktop-first with mobile optimization
- Breakpoints:
  - Mobile: < 768px
  - Tablet: 768px - 1024px
  - Desktop: > 1024px
- Touch-friendly controls for mobile

## 🔐 Security

1. **Authentication**
   - JWT tokens with refresh mechanism
   - Secure storage in httpOnly cookies
   - Session timeout

2. **API Security**
   - CORS configuration
   - Rate limiting
   - Input validation

3. **Wallet Security**
   - Never expose private keys
   - Read-only wallet display
   - Transaction signing on backend

## 🚀 Development Phases

### Phase 1: Foundation (Week 1)
- [ ] Setup React + TypeScript project
- [ ] Configure build tools and linting
- [ ] Implement basic routing
- [ ] Create UI component library
- [ ] Setup state management

### Phase 2: Core Features (Week 2)
- [ ] Dashboard with metrics
- [ ] Real-time WebSocket integration
- [ ] Trade history display
- [ ] Basic strategy controls

### Phase 3: Advanced Features (Week 3)
- [ ] Trading terminal
- [ ] Advanced analytics
- [ ] Strategy configuration
- [ ] Performance charts

### Phase 4: Polish (Week 4)
- [ ] Authentication system
- [ ] Error handling
- [ ] Loading states
- [ ] Mobile optimization
- [ ] Testing

## 📦 Package.json Example

```json
{
  "name": "sniperbot-frontend",
  "version": "1.0.0",
  "scripts": {
    "dev": "vite",
    "build": "tsc && vite build",
    "preview": "vite preview",
    "test": "vitest",
    "lint": "eslint src --ext ts,tsx"
  },
  "dependencies": {
    "react": "^18.2.0",
    "react-dom": "^18.2.0",
    "react-router-dom": "^6.8.0",
    "axios": "^1.3.0",
    "socket.io-client": "^4.5.0",
    "recharts": "^2.5.0",
    "antd": "^5.2.0",
    "zustand": "^4.3.0",
    "@tanstack/react-query": "^4.24.0"
  },
  "devDependencies": {
    "@types/react": "^18.0.27",
    "@types/react-dom": "^18.0.10",
    "@vitejs/plugin-react": "^3.1.0",
    "typescript": "^4.9.5",
    "vite": "^4.1.0",
    "tailwindcss": "^3.2.4"
  }
}
```

## 🎯 Success Metrics

1. **Performance**
   - Page load < 2s
   - WebSocket latency < 100ms
   - 60fps animations

2. **Usability**
   - Intuitive navigation
   - Clear error messages
   - Responsive design

3. **Reliability**
   - 99.9% uptime
   - Graceful error handling
   - Offline capability