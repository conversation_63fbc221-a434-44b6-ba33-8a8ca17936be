# SniperBot Documentation

Welcome to the SniperBot documentation. This directory contains comprehensive information about the bot's architecture, features, and development plans.

## 📚 Documentation Index

### Core Documentation
- [**CURRENT_STATE.md**](./CURRENT_STATE.md) - Complete overview of the bot's current implementation
- [**CHANGELOG.md**](./CHANGELOG.md) - Version history and recent changes

### Development Plans
- [**FRONTEND_PLAN.md**](./FRONTEND_PLAN.md) - Detailed plan for web dashboard development
- [**TESTING_PLAN.md**](./TESTING_PLAN.md) - Comprehensive testing strategy and implementation

## 🚀 Quick Links

### Getting Started
1. Read [CURRENT_STATE.md](./CURRENT_STATE.md) for understanding the architecture
2. Check the main [README.md](../README.md) for setup instructions
3. Review [config.toml](../config.toml) for configuration options

### For Developers
1. Review [TESTING_PLAN.md](./TESTING_PLAN.md) before contributing
2. Check [FRONTEND_PLAN.md](./FRONTEND_PLAN.md) for UI development
3. Follow changes in [CHANGELOG.md](./CHANGELOG.md)

## 📊 Bot Status Summary

- **Version**: 0.1.0
- **Strategies**: MicroBot (0.4 SOL), Meteora DAMM
- **RPC Provider**: Helius (Premium)
- **Default Mode**: Dry-run (safe testing)
- **API**: REST endpoints on port 8080

## 🔧 Key Features

1. **Modular Strategy System**
   - Pluggable strategies via async trait
   - Real-time performance tracking
   - Risk management built-in

2. **Helius Integration**
   - Premium RPC endpoints
   - Token risk analysis
   - Holder distribution tracking
   - WebSocket support

3. **Local Memory System**
   - SQLite-based storage
   - Trade history
   - Learning capabilities

4. **API & Monitoring**
   - REST API for control
   - Real-time metrics
   - Remote management

## 🛡️ Security Notes

- Never commit private keys
- API keys in environment variables
- Always test in dry-run mode first
- Review risk scores before trading

## 📈 Performance Metrics

Current simulation results (dry-run mode):
- MicroBot: ~70% win rate
- Meteora: Continuous fee collection
- Average response time: <100ms

## 🔮 Roadmap

1. **Phase 1** ✅ - Core bot implementation
2. **Phase 2** ✅ - Helius integration
3. **Phase 3** 🚧 - Frontend dashboard
4. **Phase 4** 📋 - Comprehensive testing
5. **Phase 5** 📋 - Production deployment

## 📞 Support

For issues or questions:
1. Check documentation first
2. Review existing GitHub issues
3. Create new issue with details

---

Last Updated: 2025-05-26