# Changelog

All notable changes to SniperBot will be documented in this file.

## [Unreleased]

### Added
- Helius RPC integration with premium endpoints
- Helius API client for enhanced token analysis
- WebSocket configuration for real-time updates
- Comprehensive documentation in `/docs` folder
- Risk assessment system using Helius data
- Token holder distribution analysis
- Enhanced transaction monitoring

### Changed
- Updated RPC configuration to prioritize Helius endpoints
- Improved RPC client initialization with failover support
- Enhanced config structure with WebSocket URL support

### Security
- API key properly configured for Helius services
- Maintained secure wallet handling practices

## [0.1.0] - 2025-05-26

### Added
- Initial project structure with Rust/Cargo setup
- Core bot engine with strategy pattern
- MicroBot strategy for aggressive meme token trading
- Meteora DAMM strategy for liquidity provision
- Local memory system using SQLite
- REST API for monitoring and control
- Docker support with docker-compose
- Configuration management via TOML
- Trade logging and history tracking

### Features
- Modular strategy system with async trait
- Wallet management (env var or JSON file)
- RPC endpoint configuration with fallback
- Dry-run mode for safe testing
- Performance metrics tracking
- Graceful shutdown handling
- API endpoints for health, metrics, and status

### Technical Stack
- Solana SDK 1.18
- Tokio async runtime
- Axum web framework
- SQLx for database operations
- Jupiter integration for swaps
- Comprehensive error handling

### Configuration
- Strategy selection (MicroBot/Meteora)
- Trading parameters (slippage, fees)
- Position sizing and risk management
- Safety-first approach with dry-run default