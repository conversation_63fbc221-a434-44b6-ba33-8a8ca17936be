# SniperBot - Current State Documentation

## 🚀 Project Overview

SniperBot is an advanced Solana trading bot with modular strategy architecture, designed for automated trading with focus on new token opportunities and liquidity provision.

## 📊 Current Status

- **Version**: 0.1.0
- **Status**: Development/Testing Phase
- **Default Mode**: Dry-run (simulation)
- **Supported Strategies**: MicroBot, Meteora DAMM
- **RPC Provider**: Helius (Premium)

## 🏗️ Architecture

### Core Components

1. **Main Entry Point** (`src/main.rs`)
   - Initializes logging and configuration
   - Creates bot instance with selected strategy
   - Starts API server for monitoring
   - Runs main trading loop

2. **Bot Engine** (`src/core/sniper_bot.rs`)
   - Manages wallet and RPC connections
   - Executes strategy iterations
   - Tracks performance metrics
   - Handles graceful shutdown

3. **Strategy System** (`src/strategies/`)
   - **Trait-based design** for pluggable strategies
   - **MicroBot**: Aggressive meme token trading (0.4 SOL)
   - **Meteora DAMM**: Dynamic AMM liquidity provision
   - Shared base functionality for trade logging

4. **Memory System** (`src/memory/`)
   - SQLite-based local storage
   - Trade history tracking
   - Token analysis caching
   - Performance learning

5. **API Server** (`src/api/`)
   - REST endpoints for monitoring
   - WebSocket support (planned)
   - Remote control capabilities

6. **Helius Integration** (`src/helius/`)
   - Token metadata retrieval
   - Risk assessment
   - Holder analysis
   - Transaction monitoring
   - Webhook support

## 🔧 Configuration

### Config Structure (`config.toml`)

```toml
# Strategy selection
strategy = "microbot"  # or "meteora"

[wallet]
private_key_path = "./.keys/wallet.json"
use_env_key = true  # Check SOLANA_PRIVATE_KEY env var first

[rpc]
endpoints = [
    "https://mainnet.helius-rpc.com/?api-key=40a78e4c-bdd0-4338-877a-aa7d56a5f5a0",
    "https://viva-z2t8up-fast-mainnet.helius-rpc.com",
    "https://staked.helius-rpc.com?api-key=40a78e4c-bdd0-4338-877a-aa7d56a5f5a0",
    "https://api.mainnet-beta.solana.com"  # Fallback
]
helius_api_key = "40a78e4c-bdd0-4338-877a-aa7d56a5f5a0"
websocket_url = "wss://mainnet.helius-rpc.com/?api-key=40a78e4c-bdd0-4338-877a-aa7d56a5f5a0"

[trading]
slippage_bps = 500  # 5%
priority_fee_lamports = 5000
dry_run = true  # IMPORTANT: Set to false for real trading

[microbot]
initial_capital_sol = 0.4
position_size_percent = 80.0
stop_loss_percent = 10.0
take_profit_targets = [50.0, 100.0, 200.0]
max_token_age_minutes = 5
min_liquidity_usd = 1000.0

[meteora]
min_pool_liquidity_usd = 10000.0
max_initial_fee_bps = 1000
position_size_usd = 500.0
max_impermanent_loss_percent = 20.0
compound_threshold_usd = 50.0
```

## 📦 Dependencies

### Core Dependencies
- **solana-sdk** (1.18): Solana blockchain interaction
- **tokio** (1.35): Async runtime
- **axum** (0.7): Web framework
- **sqlx** (0.7): Database operations
- **reqwest** (0.11): HTTP client

### Trading Dependencies
- **jupiter-swap-api-client**: Token swaps
- **spl-token**: SPL token operations
- **anchor-client**: Anchor program interaction

## 🛠️ Recent Changes

### Helius Integration (Latest)
1. Added premium RPC endpoints with API key
2. Created Helius client module for enhanced features:
   - Token metadata and risk analysis
   - Holder distribution tracking
   - Enhanced transaction monitoring
   - Webhook support for real-time updates
3. Updated RPC client initialization with failover
4. Added WebSocket configuration

### Previous Updates
- Implemented MicroBot strategy for small capital trading
- Added Meteora DAMM strategy for liquidity provision
- Created local memory system with SQLite
- Built REST API for monitoring and control
- Added Docker support

## 🚦 API Endpoints

- `GET /health` - Health check
- `GET /metrics` - Trading metrics
- `GET /status` - Bot status
- `POST /stop` - Stop bot

## 📈 Strategy Details

### MicroBot Strategy
- **Capital**: 0.4 SOL
- **Focus**: New meme tokens (<5 min old)
- **Position Size**: 80% of balance
- **Risk Management**: 10% stop loss, multiple take profits
- **Simulation**: 70% win rate in dry-run

### Meteora DAMM Strategy
- **Focus**: Early liquidity provision
- **Dynamic Fees**: Adapts to volatility
- **Position Management**: Auto-compound at threshold
- **Risk Assessment**: IL vs fee APR calculation

## 🐛 Known Issues

1. **Simulation Mode**: Currently uses random data for testing
2. **Real Trading**: Not yet tested with real funds
3. **WebSocket**: Not fully implemented
4. **Frontend**: Missing (planned)

## 🔒 Security Considerations

1. **Private Keys**: Never commit to repository
2. **API Keys**: Store in environment variables
3. **Dry Run**: Always test strategies first
4. **Rate Limits**: Respect RPC limits
5. **Slippage**: Configure appropriately

## 📝 Environment Variables

```bash
# Required for wallet
SOLANA_PRIVATE_KEY=your_base58_private_key

# Optional
RUST_LOG=info
```

## 🏃 Running the Bot

```bash
# Development
cargo run

# Production (dry-run)
cargo run --release

# Real trading (CAREFUL!)
# Edit config.toml: dry_run = false
cargo run --release

# Docker
docker-compose up -d
```

## 📊 Database Schema

### trades
- id: TEXT PRIMARY KEY
- timestamp: TEXT
- token_address: TEXT
- token_symbol: TEXT
- action: TEXT (Buy/Sell/AddLiquidity/RemoveLiquidity)
- amount_sol: REAL
- price: REAL
- tx_signature: TEXT
- pnl_sol: REAL (nullable)
- notes: TEXT (nullable)

### memories
- id: INTEGER PRIMARY KEY
- memory_type: TEXT
- content: TEXT
- metadata: TEXT (JSON)
- created_at: TEXT
- expires_at: TEXT (nullable)

### token_analysis
- token_address: TEXT PRIMARY KEY
- risk_score: REAL
- analysis_data: TEXT (JSON)
- analyzed_at: TEXT
- updated_at: TEXT

## 🔮 Next Steps

1. Implement frontend dashboard
2. Add comprehensive testing suite
3. Integrate real Jupiter swaps
4. Implement WebSocket for real-time data
5. Add more trading strategies
6. Performance optimization
7. Risk management improvements