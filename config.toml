# SniperBot Configuration

# Strategy to use: "microbot" or "meteora"
strategy = "microbot"

[wallet]
# Path to wallet JSON file (Solana CLI format)
private_key_path = "./.keys/wallet.json"
# If true, will try to read private key from SOLANA_PRIVATE_KEY env var first
use_env_key = true

[rpc]
# RPC endpoints (will use first available)
endpoints = [
    "https://mainnet.helius-rpc.com/?api-key=40a78e4c-bdd0-4338-877a-aa7d56a5f5a0",
    "https://viva-z2t8up-fast-mainnet.helius-rpc.com",
    "https://staked.helius-rpc.com?api-key=40a78e4c-bdd0-4338-877a-aa7d56a5f5a0",
    "https://api.mainnet-beta.solana.com"  # Fallback
]
# Helius API key for enhanced features
helius_api_key = "40a78e4c-bdd0-4338-877a-aa7d56a5f5a0"
# WebSocket endpoint for real-time data
websocket_url = "wss://mainnet.helius-rpc.com/?api-key=40a78e4c-bdd0-4338-877a-aa7d56a5f5a0"

[trading]
# Slippage tolerance in basis points (100 = 1%)
slippage_bps = 500
# Priority fee in lamports
priority_fee_lamports = 5000
# If true, won't execute real transactions
dry_run = true

[microbot]
# Starting capital in SOL
initial_capital_sol = 0.4
# Position size as percentage of balance
position_size_percent = 80.0
# Stop loss percentage
stop_loss_percent = 10.0
# Take profit targets (%)
take_profit_targets = [50.0, 100.0, 200.0]
# Maximum token age in minutes
max_token_age_minutes = 5
# Minimum liquidity in USD
min_liquidity_usd = 1000.0

[meteora]
# Minimum pool liquidity in USD
min_pool_liquidity_usd = 10000.0
# Maximum initial fee in basis points
max_initial_fee_bps = 1000
# Position size per pool in USD
position_size_usd = 500.0
# Maximum acceptable impermanent loss (%)
max_impermanent_loss_percent = 20.0
# Fee threshold for compounding
compound_threshold_usd = 50.0
