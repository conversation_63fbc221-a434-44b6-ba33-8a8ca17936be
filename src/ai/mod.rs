use anyhow::Result;
use reqwest::Client;
use serde::{Deserialize, Serialize};
use serde_json::Value as JsonValue;
use std::collections::HashMap;
use tracing::{info, debug, warn};

use crate::openmemory::{OpenMemoryClient, Memory};

/// AI Decision Engine - wykorzystuje Qwen3 do analizy i podejmowania decyzji
pub struct AiDecisionEngine {
    qwen_client: QwenClient,
    memory_client: OpenMemoryClient,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct TokenAnalysis {
    pub token_address: String,
    pub recommendation: Recommendation,
    pub confidence: f64,
    pub risk_score: f64,
    pub potential_profit: f64,
    pub reasoning: String,
    pub similar_tokens_analysis: Vec<SimilarTokenOutcome>,
}

#[derive(Debug, Serialize, Deserialize, PartialEq)]
#[serde(rename_all = "lowercase")]
pub enum Recommendation {
    StrongBuy,
    Buy,
    Hold,
    Sell,
    Avoid,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct SimilarTokenOutcome {
    pub token_symbol: String,
    pub outcome: String,
    pub profit_percentage: f64,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct MarketAnalysis {
    pub current_condition: String,
    pub trend: String,
    pub volatility: String,
    pub recommended_strategies: Vec<String>,
    pub risk_level: String,
}

struct QwenClient {
    base_url: String,
    api_key: String,
    client: Client,
}

#[derive(Debug, Serialize)]
struct QwenRequest {
    model: String,
    prompt: String,
    temperature: f64,
    max_tokens: u32,
}

#[derive(Debug, Deserialize)]
struct QwenResponse {
    choices: Vec<QwenChoice>,
}

#[derive(Debug, Deserialize)]
struct QwenChoice {
    text: String,
}

impl AiDecisionEngine {
    pub fn new(qwen_api_key: &str, qwen_base_url: &str, memory_client: OpenMemoryClient) -> Self {
        info!("🤖 Initializing AI Decision Engine with Qwen3");
        
        Self {
            qwen_client: QwenClient {
                base_url: qwen_base_url.to_string(),
                api_key: qwen_api_key.to_string(),
                client: Client::new(),
            },
            memory_client,
        }
    }
    
    /// Analizuje token z wykorzystaniem pamięci historycznej
    pub async fn analyze_token_with_memory(
        &self,
        token_data: &TokenData,
    ) -> Result<TokenAnalysis> {
        // Wyszukaj podobne tokeny w pamięci
        let similar_tokens = self.memory_client
            .search_memories(
                &format!("token similar to {} {}", token_data.symbol, token_data.name),
                5
            )
            .await?;
        
        // Przygotuj kontekst dla modelu AI
        let memory_context = self.format_memory_context(&similar_tokens);
        
        // Przygotuj prompt dla Qwen3
        let prompt = format!(
            r#"Analizuję nowy token na Solanie z następującymi cechami:
- Nazwa: {}
- Symbol: {}
- Adres twórcy: {}
- Początkowa płynność: {} USD
- Funkcje kontraktu: {:?}
- Wiek: {} minut
- Aktualna cena: {}
- Wolumen 24h: {} USD

Na podstawie wcześniejszych obserwacji, wiem:
{}

Biorąc pod uwagę powyższe informacje:
1. Oceń potencjał i ryzyko tego tokenu (skala 0-1)
2. Podaj rekomendację: StrongBuy/Buy/Hold/Sell/Avoid
3. Oszacuj potencjalny zysk w procentach
4. Wyjaśnij swoje rozumowanie

Odpowiedz w formacie JSON:
{{
    "risk_score": 0.0-1.0,
    "recommendation": "StrongBuy/Buy/Hold/Sell/Avoid",
    "confidence": 0.0-1.0,
    "potential_profit": percentage,
    "reasoning": "szczegółowe wyjaśnienie"
}}"#,
            token_data.name,
            token_data.symbol,
            &token_data.creator_address[..8],
            token_data.initial_liquidity,
            token_data.contract_features,
            token_data.age_minutes,
            token_data.current_price,
            token_data.volume_24h,
            memory_context
        );
        
        // Wywołaj Qwen3
        let response = self.qwen_client.complete(&prompt).await?;
        
        // Parsuj odpowiedź
        let analysis = self.parse_ai_response(&response, token_data)?;
        
        // Zapisz wynik analizy do pamięci
        self.save_analysis_to_memory(&analysis).await?;
        
        Ok(analysis)
    }
    
    /// Analizuje aktualne warunki rynkowe
    pub async fn analyze_market_conditions(
        &self,
        market_data: &MarketData,
    ) -> Result<MarketAnalysis> {
        // Pobierz historyczne dane o warunkach rynkowych
        let market_memories = self.memory_client
            .search_memories("market condition", 10)
            .await?;
        
        let prompt = format!(
            r#"Analizuję aktualne warunki rynkowe Solany:
- Cena SOL: {} USD (zmiana 24h: {:.1}%)
- Całkowity wolumen DEX 24h: {} USD
- Liczba nowych tokenów dzisiaj: {}
- Średni APY na farmach: {:.1}%
- Dominacja SOL: {:.1}%
- Wskaźnik strachu i chciwości: {}

Historyczne obserwacje:
{}

Określ:
1. Aktualny stan rynku (bull/bear/neutral)
2. Trend (upward/downward/sideways)
3. Poziom zmienności (low/medium/high)
4. Rekomendowane strategie
5. Poziom ryzyka (low/medium/high)

Odpowiedz w formacie JSON."#,
            market_data.sol_price,
            market_data.sol_24h_change,
            market_data.dex_volume_24h,
            market_data.new_tokens_today,
            market_data.avg_farm_apy,
            market_data.sol_dominance,
            market_data.fear_greed_index,
            self.format_memory_context(&market_memories)
        );
        
        let response = self.qwen_client.complete(&prompt).await?;
        let analysis: MarketAnalysis = serde_json::from_str(&response)?;
        
        Ok(analysis)
    }
    
    /// Przewiduje optymalne slippage dla transakcji
    pub async fn predict_optimal_slippage(
        &self,
        token_address: &str,
        trade_size_sol: f64,
    ) -> Result<u16> {
        // Pobierz historyczne dane o slippage dla podobnych transakcji
        let slippage_memories = self.memory_client
            .search_memories(&format!("slippage for token {}", token_address), 10)
            .await?;
        
        let prompt = format!(
            r#"Na podstawie historycznych danych o slippage:
{}

Dla transakcji o wielkości {} SOL na token {},
jaki jest optymalny slippage w basis points (bps)?
Weź pod uwagę aktualną zmienność i płynność.

Odpowiedz tylko liczbą (np. 500 dla 5%)."#,
            self.format_memory_context(&slippage_memories),
            trade_size_sol,
            &token_address[..8]
        );
        
        let response = self.qwen_client.complete(&prompt).await?;
        let slippage_bps: u16 = response.trim().parse()?;
        
        Ok(slippage_bps.min(2000)) // Max 20%
    }
    
    fn format_memory_context(&self, memories: &[Memory]) -> String {
        memories
            .iter()
            .map(|mem| format!("- {}", mem.content))
            .collect::<Vec<_>>()
            .join("\n")
    }
    
    fn parse_ai_response(&self, response: &str, token_data: &TokenData) -> Result<TokenAnalysis> {
        let parsed: JsonValue = serde_json::from_str(response)?;
        
        Ok(TokenAnalysis {
            token_address: token_data.address.clone(),
            recommendation: serde_json::from_value(parsed["recommendation"].clone())?,
            confidence: parsed["confidence"].as_f64().unwrap_or(0.5),
            risk_score: parsed["risk_score"].as_f64().unwrap_or(0.5),
            potential_profit: parsed["potential_profit"].as_f64().unwrap_or(0.0),
            reasoning: parsed["reasoning"].as_str().unwrap_or("").to_string(),
            similar_tokens_analysis: vec![], // Wypełniane osobno
        })
    }
    
    async fn save_analysis_to_memory(&self, analysis: &TokenAnalysis) -> Result<()> {
        let content = format!(
            "Analiza tokenu {}: {} z confidence {:.1}%",
            &analysis.token_address[..8],
            match &analysis.recommendation {
                Recommendation::StrongBuy => "Strong Buy",
                Recommendation::Buy => "Buy",
                Recommendation::Hold => "Hold",
                Recommendation::Sell => "Sell",
                Recommendation::Avoid => "Avoid",
            },
            analysis.confidence * 100.0
        );
        
        let mut metadata = HashMap::new();
        metadata.insert("token_address".to_string(), JsonValue::String(analysis.token_address.clone()));
        metadata.insert("recommendation".to_string(), serde_json::to_value(&analysis.recommendation)?);
        metadata.insert("confidence".to_string(), JsonValue::Number(serde_json::Number::from_f64(analysis.confidence).unwrap()));
        metadata.insert("risk_score".to_string(), JsonValue::Number(serde_json::Number::from_f64(analysis.risk_score).unwrap()));
        
        self.memory_client.add_memory(&content, Some(metadata)).await
    }
}

impl QwenClient {
    async fn complete(&self, prompt: &str) -> Result<String> {
        let request = QwenRequest {
            model: "qwen3-8b".to_string(),
            prompt: prompt.to_string(),
            temperature: 0.1,
            max_tokens: 1024,
        };
        
        let response = self.client
            .post(&format!("{}/completions", self.base_url))
            .header("Authorization", format!("Bearer {}", self.api_key))
            .json(&request)
            .send()
            .await?;
        
        if response.status().is_success() {
            let qwen_response: QwenResponse = response.json().await?;
            Ok(qwen_response.choices.first()
                .map(|c| c.text.clone())
                .unwrap_or_default())
        } else {
            Err(anyhow::anyhow!("Qwen API error: {}", response.status()))
        }
    }
}

// Struktury danych

#[derive(Debug, Clone)]
pub struct TokenData {
    pub address: String,
    pub name: String,
    pub symbol: String,
    pub creator_address: String,
    pub initial_liquidity: f64,
    pub contract_features: Vec<String>,
    pub age_minutes: u64,
    pub current_price: f64,
    pub volume_24h: f64,
}

#[derive(Debug, Clone)]
pub struct MarketData {
    pub sol_price: f64,
    pub sol_24h_change: f64,
    pub dex_volume_24h: f64,
    pub new_tokens_today: u32,
    pub avg_farm_apy: f64,
    pub sol_dominance: f64,
    pub fear_greed_index: u32,
}
