use anyhow::Result;
use sqlx::{SqlitePool, sqlite::SqlitePoolOptions};
use chrono::{DateTime, Utc};
use serde_json::Value as JsonValue;

use crate::strategies::TradeRecord;

/// Lightweight local memory storage (alternative to OpenMemory)
pub struct LocalMemory {
    pool: SqlitePool,
}

impl LocalMemory {
    pub async fn new(db_path: &str) -> Result<Self> {
        // Create directory if needed
        if let Some(parent) = std::path::Path::new(db_path).parent() {
            std::fs::create_dir_all(parent)?;
        }
        
        // Connect to SQLite
        let pool = SqlitePoolOptions::new()
            .max_connections(5)
            .connect(&format!("sqlite:{}", db_path))
            .await?;
        
        // Initialize schema
        Self::init_schema(&pool).await?;
        
        Ok(Self { pool })
    }
    
    async fn init_schema(pool: &SqlitePool) -> Result<()> {
        // Trades table
        sqlx::query(
            r#"
            CREATE TABLE IF NOT EXISTS trades (
                id TEXT PRIMARY KEY,
                timestamp TEXT NOT NULL,
                token_address TEXT NOT NULL,
                token_symbol TEXT NOT NULL,
                action TEXT NOT NULL,
                amount_sol REAL NOT NULL,
                price REAL NOT NULL,
                tx_signature TEXT NOT NULL,
                pnl_sol REAL,
                notes TEXT,
                created_at TEXT DEFAULT CURRENT_TIMESTAMP
            )
            "#
        )
        .execute(pool)
        .await?;
        
        // General memory table (for learning)
        sqlx::query(
            r#"
            CREATE TABLE IF NOT EXISTS memories (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                memory_type TEXT NOT NULL,
                content TEXT NOT NULL,
                metadata TEXT,
                created_at TEXT DEFAULT CURRENT_TIMESTAMP,
                expires_at TEXT
            )
            "#
        )
        .execute(pool)
        .await?;
        
        // Token analysis cache
        sqlx::query(
            r#"
            CREATE TABLE IF NOT EXISTS token_analysis (
                token_address TEXT PRIMARY KEY,
                risk_score REAL NOT NULL,
                analysis_data TEXT NOT NULL,
                analyzed_at TEXT NOT NULL,
                updated_at TEXT DEFAULT CURRENT_TIMESTAMP
            )
            "#
        )
        .execute(pool)
        .await?;
        
        Ok(())
    }
    
    pub async fn save_trade(&self, trade: &TradeRecord) -> Result<()> {
        sqlx::query(
            r#"
            INSERT INTO trades (
                id, timestamp, token_address, token_symbol, 
                action, amount_sol, price, tx_signature, pnl_sol, notes
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            "#
        )
        .bind(&trade.id)
        .bind(trade.timestamp.to_rfc3339())
        .bind(&trade.token_address)
        .bind(&trade.token_symbol)
        .bind(format!("{:?}", trade.action))
        .bind(trade.amount_sol)
        .bind(trade.price)
        .bind(&trade.tx_signature)
        .bind(trade.pnl_sol)
        .bind(&trade.notes)
        .execute(&self.pool)
        .await?;
        
        Ok(())
    }
    
    pub async fn get_trades_for_token(&self, token_address: &str) -> Result<Vec<TradeRecord>> {
        let rows = sqlx::query_as::<_, TradeRow>(
            r#"
            SELECT * FROM trades 
            WHERE token_address = ? 
            ORDER BY timestamp DESC
            "#
        )
        .bind(token_address)
        .fetch_all(&self.pool)
        .await?;
        
        Ok(rows.into_iter().map(|r| r.into()).collect())
    }
    
    pub async fn save_memory(&self, memory_type: &str, content: &str, ttl_seconds: Option<i64>) -> Result<()> {
        let expires_at = ttl_seconds.map(|ttl| {
            (Utc::now() + chrono::Duration::seconds(ttl)).to_rfc3339()
        });
        
        sqlx::query(
            r#"
            INSERT INTO memories (memory_type, content, expires_at)
            VALUES (?, ?, ?)
            "#
        )
        .bind(memory_type)
        .bind(content)
        .bind(expires_at)
        .execute(&self.pool)
        .await?;
        
        Ok(())
    }
    
    pub async fn search_memories(&self, memory_type: &str, limit: i32) -> Result<Vec<String>> {
        let rows = sqlx::query_scalar::<_, String>(
            r#"
            SELECT content FROM memories
            WHERE memory_type = ?
            AND (expires_at IS NULL OR expires_at > datetime('now'))
            ORDER BY created_at DESC
            LIMIT ?
            "#
        )
        .bind(memory_type)
        .bind(limit)
        .fetch_all(&self.pool)
        .await?;
        
        Ok(rows)
    }
    
    pub async fn save_token_analysis(&self, token_address: &str, risk_score: f64, analysis_data: &JsonValue) -> Result<()> {
        let data_str = serde_json::to_string(analysis_data)?;
        
        sqlx::query(
            r#"
            INSERT OR REPLACE INTO token_analysis 
            (token_address, risk_score, analysis_data, analyzed_at)
            VALUES (?, ?, ?, ?)
            "#
        )
        .bind(token_address)
        .bind(risk_score)
        .bind(data_str)
        .bind(Utc::now().to_rfc3339())
        .execute(&self.pool)
        .await?;
        
        Ok(())
    }
    
    pub async fn cleanup_expired(&self) -> Result<()> {
        sqlx::query(
            r#"
            DELETE FROM memories 
            WHERE expires_at IS NOT NULL 
            AND expires_at < datetime('now')
            "#
        )
        .execute(&self.pool)
        .await?;
        
        Ok(())
    }
}

// Helper struct for SQLx
#[derive(sqlx::FromRow)]
struct TradeRow {
    id: String,
    timestamp: String,
    token_address: String,
    token_symbol: String,
    action: String,
    amount_sol: f64,
    price: f64,
    tx_signature: String,
    pnl_sol: Option<f64>,
    notes: Option<String>,
}

impl From<TradeRow> for TradeRecord {
    fn from(row: TradeRow) -> Self {
        use crate::strategies::TradeAction;
        
        let action = match row.action.as_str() {
            "Buy" => TradeAction::Buy,
            "Sell" => TradeAction::Sell,
            "AddLiquidity" => TradeAction::AddLiquidity,
            "RemoveLiquidity" => TradeAction::RemoveLiquidity,
            _ => TradeAction::Buy,
        };
        
        TradeRecord {
            id: row.id,
            timestamp: DateTime::parse_from_rfc3339(&row.timestamp)
                .unwrap_or_else(|_| Utc::now().into())
                .with_timezone(&Utc),
            token_address: row.token_address,
            token_symbol: row.token_symbol,
            action,
            amount_sol: row.amount_sol,
            price: row.price,
            tx_signature: row.tx_signature,
            pnl_sol: row.pnl_sol,
            notes: row.notes,
        }
    }
}
