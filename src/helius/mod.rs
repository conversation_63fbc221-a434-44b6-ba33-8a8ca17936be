use anyhow::Result;
use reqwest::Client;
use serde::{Deserialize, Serialize};
use solana_sdk::pubkey::Pubkey;
use std::collections::HashMap;

pub struct HeliusClient {
    api_key: String,
    client: Client,
    base_url: String,
}

impl HeliusClient {
    pub fn new(api_key: String, _rpc_url: String) -> Result<Self> {
        Ok(Self {
            api_key,
            client: Client::new(),
            base_url: "https://api.helius.xyz/v0".to_string(),
        })
    }

    /// Get token metadata including risk analysis
    pub async fn get_token_metadata(&self, mint: &Pubkey) -> Result<TokenMetadata> {
        let url = format!("{}/token-metadata?api-key={}", self.base_url, self.api_key);
        let body = serde_json::json!({
            "mintAccounts": [mint.to_string()]
        });

        let response = self.client
            .post(&url)
            .json(&body)
            .send()
            .await?
            .json::<Vec<TokenMetadata>>()
            .await?;

        response.into_iter().next()
            .ok_or_else(|| anyhow::anyhow!("No metadata found"))
    }

    /// Get recent transactions for a token
    pub async fn get_token_transactions(&self, mint: &Pubkey, limit: u32) -> Result<Vec<EnhancedTransaction>> {
        let url = format!("{}/addresses/{}/transactions?api-key={}&limit={}",
            self.base_url, mint, self.api_key, limit);

        let response = self.client
            .get(&url)
            .send()
            .await?
            .json()
            .await?;

        Ok(response)
    }

    /// Get token holders distribution
    pub async fn get_token_holders(&self, mint: &Pubkey) -> Result<TokenHolders> {
        let url = format!("{}/token/{}/holders?api-key={}",
            self.base_url, mint, self.api_key);

        let response = self.client
            .get(&url)
            .send()
            .await?
            .json()
            .await?;

        Ok(response)
    }

    /// Get DeFi positions for analysis
    pub async fn get_defi_positions(&self, owner: &Pubkey) -> Result<Vec<DeFiPosition>> {
        let url = format!("{}/addresses/{}/balances?api-key={}",
            self.base_url, owner, self.api_key);

        let response = self.client
            .get(&url)
            .send()
            .await?
            .json()
            .await?;

        Ok(response)
    }

    /// Subscribe to account changes via webhook
    pub async fn create_webhook(&self, accounts: Vec<String>, webhook_url: &str) -> Result<String> {
        let url = format!("{}/webhooks?api-key={}", self.base_url, self.api_key);
        let body = serde_json::json!({
            "webhookURL": webhook_url,
            "accountAddresses": accounts,
            "webhookType": "enhanced"
        });

        let response = self.client
            .post(&url)
            .json(&body)
            .send()
            .await?
            .json::<WebhookResponse>()
            .await?;

        Ok(response.webhook_id)
    }
}

#[derive(Debug, Serialize, Deserialize)]
pub struct TokenMetadata {
    pub mint: String,
    pub symbol: String,
    pub name: String,
    pub decimals: u8,
    pub token_program: String,
    pub metadata: Option<MetadataDetails>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct MetadataDetails {
    pub update_authority: String,
    pub mint: String,
    pub name: String,
    pub symbol: String,
    pub uri: String,
    pub seller_fee_basis_points: u16,
    pub creators: Option<Vec<Creator>>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct Creator {
    pub address: String,
    pub verified: bool,
    pub share: u8,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct EnhancedTransaction {
    pub signature: String,
    pub timestamp: i64,
    pub fee: u64,
    pub slot: u64,
    pub token_transfers: Vec<TokenTransfer>,
    pub native_transfers: Vec<NativeTransfer>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct TokenTransfer {
    pub from_address: String,
    pub to_address: String,
    pub from_token_account: String,
    pub to_token_account: String,
    pub token_amount: f64,
    pub mint: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct NativeTransfer {
    pub from_address: String,
    pub to_address: String,
    pub amount: u64,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct TokenHolders {
    pub total_holders: u32,
    pub total_supply: u64,
    pub top_holders: Vec<Holder>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct Holder {
    pub owner: String,
    pub balance: u64,
    pub percentage: f64,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct DeFiPosition {
    pub protocol: String,
    pub position_type: String,
    pub value_usd: f64,
    pub tokens: Vec<TokenBalance>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct TokenBalance {
    pub mint: String,
    pub amount: f64,
    pub decimals: u8,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct WebhookResponse {
    pub webhook_id: String,
    pub webhook_url: String,
}

/// Risk assessment based on Helius data
pub async fn assess_token_risk(client: &HeliusClient, mint: &Pubkey) -> Result<RiskAssessment> {
    // Get metadata
    let metadata = client.get_token_metadata(mint).await?;

    // Get holder distribution
    let holders = client.get_token_holders(mint).await?;

    // Get recent transactions
    let transactions = client.get_token_transactions(mint, 100).await?;

    // Calculate risk factors
    let mut risk_score = 0.0;
    let mut risk_factors = vec![];

    // Check freeze authority
    if metadata.metadata.is_some() {
        if let Some(meta) = &metadata.metadata {
            if meta.update_authority != "11111111111111111111111111111111" {
                risk_score += 0.2;
                risk_factors.push("Active update authority".to_string());
            }
        }
    }

    // Check concentration
    if let Some(top_holder) = holders.top_holders.first() {
        if top_holder.percentage > 50.0 {
            risk_score += 0.3;
            risk_factors.push(format!("High concentration: {:.1}%", top_holder.percentage));
        }
    }

    // Check holder count
    if holders.total_holders < 50 {
        risk_score += 0.2;
        risk_factors.push(format!("Low holder count: {}", holders.total_holders));
    }

    // Check transaction patterns
    let unique_traders: std::collections::HashSet<_> = transactions.iter()
        .flat_map(|tx| {
            tx.token_transfers.iter()
                .flat_map(|t| vec![t.from_address.clone(), t.to_address.clone()])
        })
        .collect();

    if unique_traders.len() < 20 {
        risk_score += 0.1;
        risk_factors.push("Low trading activity".to_string());
    }

    Ok(RiskAssessment {
        risk_score: risk_score.min(1.0),
        risk_factors,
        metadata,
        holder_distribution: holders,
        recent_activity: transactions.len() as u32,
    })
}

#[derive(Debug)]
pub struct RiskAssessment {
    pub risk_score: f64,
    pub risk_factors: Vec<String>,
    pub metadata: TokenMetadata,
    pub holder_distribution: TokenHolders,
    pub recent_activity: u32,
}