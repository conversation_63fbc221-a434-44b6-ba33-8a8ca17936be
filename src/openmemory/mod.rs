use anyhow::Result;
use reqwest::Client;
use serde::{Deserialize, Serialize};
use serde_json::Value as JsonValue;
use std::collections::HashMap;
use chrono::{DateTime, Utc, Duration};
use tracing::{info, debug, warn};

/// OpenMemory MCP Client - zarządza pamięcią bota w czasie rzeczywistym
pub struct OpenMemoryClient {
    base_url: String,
    user_id: String,
    client: Client,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct Memory {
    pub id: String,
    pub content: String,
    pub memory_type: MemoryType,
    pub metadata: HashMap<String, JsonValue>,
    pub created_at: DateTime<Utc>,
    pub ttl: Option<i64>, // TTL w sekundach
}

#[derive(Debug, Serialize, Deserialize, Clone)]
#[serde(rename_all = "snake_case")]
pub enum MemoryType {
    Token,
    Strategy,
    Pattern,
    Market,
    Trade,
}

#[derive(Debug, Serialize)]
struct AddMemoryRequest {
    content: String,
    metadata: Option<HashMap<String, JsonValue>>,
}

#[derive(Debug, Serialize)]
struct SearchMemoriesRequest {
    query: String,
    limit: usize,
}

impl OpenMemoryClient {
    pub fn new(base_url: &str, user_id: &str) -> Self {
        info!("🧠 Initializing OpenMemory MCP client");
        Self {
            base_url: base_url.to_string(),
            user_id: user_id.to_string(),
            client: Client::new(),
        }
    }
    
    /// Wyszukuje wspomnienia na podstawie zapytania
    pub async fn search_memories(&self, query: &str, limit: usize) -> Result<Vec<Memory>> {
        debug!("Searching memories: query='{}', limit={}", query, limit);
        
        let url = format!("{}/search", self.base_url);
        let request = SearchMemoriesRequest {
            query: query.to_string(),
            limit,
        };
        
        let response = self.client
            .post(&url)
            .header("X-User-Id", &self.user_id)
            .json(&request)
            .send()
            .await?;
        
        if response.status().is_success() {
            let memories: Vec<Memory> = response.json().await?;
            debug!("Found {} memories", memories.len());
            Ok(memories)
        } else {
            let error_text = response.text().await?;
            Err(anyhow::anyhow!("Failed to search memories: {}", error_text))
        }
    }
    
    /// Dodaje nowe wspomnienie
    pub async fn add_memory(&self, content: &str, metadata: Option<HashMap<String, JsonValue>>) -> Result<()> {
        debug!("Adding memory: content_len={}", content.len());
        
        let url = format!("{}/add", self.base_url);
        let request = AddMemoryRequest {
            content: content.to_string(),
            metadata,
        };
        
        let response = self.client
            .post(&url)
            .header("X-User-Id", &self.user_id)
            .json(&request)
            .send()
            .await?;
        
        if response.status().is_success() {
            info!("✅ Memory added successfully");
            Ok(())
        } else {
            let error_text = response.text().await?;
            Err(anyhow::anyhow!("Failed to add memory: {}", error_text))
        }
    }
    
    /// Dodaje wspomnienie o tokenie
    pub async fn add_token_memory(&self, token: &TokenMemory) -> Result<()> {
        let mut metadata = HashMap::new();
        metadata.insert("memory_type".to_string(), JsonValue::String("token".to_string()));
        metadata.insert("token_address".to_string(), JsonValue::String(token.token_address.clone()));
        metadata.insert("token_name".to_string(), JsonValue::String(token.token_name.clone()));
        metadata.insert("token_symbol".to_string(), JsonValue::String(token.token_symbol.clone()));
        metadata.insert("initial_price".to_string(), JsonValue::Number(serde_json::Number::from_f64(token.initial_price).unwrap()));
        metadata.insert("initial_liquidity".to_string(), JsonValue::Number(serde_json::Number::from_f64(token.initial_liquidity).unwrap()));
        metadata.insert("creator_address".to_string(), JsonValue::String(token.creator_address.clone()));
        metadata.insert("contract_features".to_string(), serde_json::to_value(&token.contract_features)?);
        metadata.insert("outcome".to_string(), JsonValue::String(token.outcome.clone()));
        metadata.insert("profit_percentage".to_string(), JsonValue::Number(serde_json::Number::from_f64(token.profit_percentage).unwrap()));
        metadata.insert("timestamp".to_string(), JsonValue::String(token.timestamp.to_rfc3339()));
        metadata.insert("ttl".to_string(), JsonValue::Number(serde_json::Number::from(token.ttl)));
        
        let content = format!(
            "Token {} ({}) created by {} - Initial price: {}, Liquidity: ${}, Outcome: {}, Profit: {:.1}%",
            token.token_symbol,
            token.token_name,
            &token.creator_address[..8],
            token.initial_price,
            token.initial_liquidity,
            token.outcome,
            token.profit_percentage
        );
        
        self.add_memory(&content, Some(metadata)).await
    }
    
    /// Dodaje wspomnienie o strategii
    pub async fn add_strategy_memory(&self, strategy: &StrategyMemory) -> Result<()> {
        let mut metadata = HashMap::new();
        metadata.insert("memory_type".to_string(), JsonValue::String("strategy".to_string()));
        metadata.insert("strategy_name".to_string(), JsonValue::String(strategy.strategy_name.clone()));
        metadata.insert("parameters".to_string(), serde_json::to_value(&strategy.parameters)?);
        metadata.insert("market_conditions".to_string(), serde_json::to_value(&strategy.market_conditions)?);
        metadata.insert("success_rate".to_string(), JsonValue::Number(serde_json::Number::from_f64(strategy.success_rate).unwrap()));
        metadata.insert("avg_profit".to_string(), JsonValue::Number(serde_json::Number::from_f64(strategy.avg_profit).unwrap()));
        metadata.insert("execution_count".to_string(), JsonValue::Number(serde_json::Number::from(strategy.execution_count)));
        metadata.insert("timestamp".to_string(), JsonValue::String(strategy.timestamp.to_rfc3339()));
        metadata.insert("ttl".to_string(), JsonValue::Number(serde_json::Number::from(strategy.ttl)));
        
        let content = format!(
            "Strategy {} - Success rate: {:.1}%, Avg profit: {:.1}%, Executions: {}",
            strategy.strategy_name,
            strategy.success_rate * 100.0,
            strategy.avg_profit,
            strategy.execution_count
        );
        
        self.add_memory(&content, Some(metadata)).await
    }
    
    /// Dodaje wspomnienie o wzorcu
    pub async fn add_pattern_memory(&self, pattern: &PatternMemory) -> Result<()> {
        let mut metadata = HashMap::new();
        metadata.insert("memory_type".to_string(), JsonValue::String("pattern".to_string()));
        metadata.insert("pattern_name".to_string(), JsonValue::String(pattern.pattern_name.clone()));
        metadata.insert("features".to_string(), serde_json::to_value(&pattern.features)?);
        metadata.insert("detection_count".to_string(), JsonValue::Number(serde_json::Number::from(pattern.detection_count)));
        metadata.insert("confidence".to_string(), JsonValue::Number(serde_json::Number::from_f64(pattern.confidence).unwrap()));
        metadata.insert("last_updated".to_string(), JsonValue::String(pattern.last_updated.to_rfc3339()));
        metadata.insert("ttl".to_string(), JsonValue::Number(serde_json::Number::from(pattern.ttl)));
        
        let content = format!(
            "Pattern {} detected {} times with {:.1}% confidence",
            pattern.pattern_name,
            pattern.detection_count,
            pattern.confidence * 100.0
        );
        
        self.add_memory(&content, Some(metadata)).await
    }
    
    /// Dodaje wspomnienie o warunkach rynkowych
    pub async fn add_market_memory(&self, market: &MarketMemory) -> Result<()> {
        let mut metadata = HashMap::new();
        metadata.insert("memory_type".to_string(), JsonValue::String("market".to_string()));
        metadata.insert("condition".to_string(), JsonValue::String(market.condition.clone()));
        metadata.insert("indicators".to_string(), serde_json::to_value(&market.indicators)?);
        metadata.insert("best_strategies".to_string(), serde_json::to_value(&market.best_strategies)?);
        metadata.insert("worst_strategies".to_string(), serde_json::to_value(&market.worst_strategies)?);
        metadata.insert("start_date".to_string(), JsonValue::String(market.start_date.to_rfc3339()));
        metadata.insert("last_updated".to_string(), JsonValue::String(market.last_updated.to_rfc3339()));
        metadata.insert("ttl".to_string(), JsonValue::Number(serde_json::Number::from(market.ttl)));
        
        let content = format!(
            "Market condition: {} - Best strategies: {:?}, Worst: {:?}",
            market.condition,
            market.best_strategies,
            market.worst_strategies
        );
        
        self.add_memory(&content, Some(metadata)).await
    }
    
    /// Wyszukuje podobne tokeny
    pub async fn find_similar_tokens(&self, token_address: &str, limit: usize) -> Result<Vec<Memory>> {
        let query = format!("token similar to {}", token_address);
        self.search_memories(&query, limit).await
    }
    
    /// Wyszukuje strategie dla danego warunku rynkowego
    pub async fn find_strategies_for_market(&self, market_condition: &str) -> Result<Vec<Memory>> {
        let query = format!("strategy performance in {} market", market_condition);
        self.search_memories(&query, 10).await
    }
    
    /// Wyszukuje wzorce rugpull
    pub async fn find_rugpull_patterns(&self) -> Result<Vec<Memory>> {
        self.search_memories("rugpull pattern", 20).await
    }
}

// Struktury danych dla różnych typów pamięci

#[derive(Debug, Serialize, Deserialize)]
pub struct TokenMemory {
    pub token_address: String,
    pub token_name: String,
    pub token_symbol: String,
    pub initial_price: f64,
    pub initial_liquidity: f64,
    pub creator_address: String,
    pub contract_features: Vec<String>,
    pub outcome: String,
    pub profit_percentage: f64,
    pub timestamp: DateTime<Utc>,
    pub ttl: i64, // 180 dni w sekundach
}

#[derive(Debug, Serialize, Deserialize)]
pub struct StrategyMemory {
    pub strategy_name: String,
    pub parameters: HashMap<String, JsonValue>,
    pub market_conditions: HashMap<String, JsonValue>,
    pub success_rate: f64,
    pub avg_profit: f64,
    pub execution_count: u64,
    pub timestamp: DateTime<Utc>,
    pub ttl: i64, // 30 dni w sekundach
}

#[derive(Debug, Serialize, Deserialize)]
pub struct PatternMemory {
    pub pattern_name: String,
    pub features: Vec<String>,
    pub detection_count: u64,
    pub confidence: f64,
    pub last_updated: DateTime<Utc>,
    pub ttl: i64, // 90 dni w sekundach
}

#[derive(Debug, Serialize, Deserialize)]
pub struct MarketMemory {
    pub condition: String,
    pub indicators: HashMap<String, JsonValue>,
    pub best_strategies: Vec<String>,
    pub worst_strategies: Vec<String>,
    pub start_date: DateTime<Utc>,
    pub last_updated: DateTime<Utc>,
    pub ttl: i64, // 30 dni w sekundach
}

impl Default for TokenMemory {
    fn default() -> Self {
        Self {
            token_address: String::new(),
            token_name: String::new(),
            token_symbol: String::new(),
            initial_price: 0.0,
            initial_liquidity: 0.0,
            creator_address: String::new(),
            contract_features: vec![],
            outcome: "unknown".to_string(),
            profit_percentage: 0.0,
            timestamp: Utc::now(),
            ttl: 15_552_000, // 180 dni
        }
    }
}

impl Default for StrategyMemory {
    fn default() -> Self {
        Self {
            strategy_name: String::new(),
            parameters: HashMap::new(),
            market_conditions: HashMap::new(),
            success_rate: 0.0,
            avg_profit: 0.0,
            execution_count: 0,
            timestamp: Utc::now(),
            ttl: 2_592_000, // 30 dni
        }
    }
}

impl Default for PatternMemory {
    fn default() -> Self {
        Self {
            pattern_name: String::new(),
            features: vec![],
            detection_count: 0,
            confidence: 0.0,
            last_updated: Utc::now(),
            ttl: 7_776_000, // 90 dni
        }
    }
}

impl Default for MarketMemory {
    fn default() -> Self {
        Self {
            condition: String::new(),
            indicators: HashMap::new(),
            best_strategies: vec![],
            worst_strategies: vec![],
            start_date: Utc::now(),
            last_updated: Utc::now(),
            ttl: 2_592_000, // 30 dni
        }
    }
}
