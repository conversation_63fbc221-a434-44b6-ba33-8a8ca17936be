use async_trait::async_trait;
use anyhow::Result;
use solana_sdk::pubkey::Pubkey;
use std::sync::Arc;
use tokio::sync::RwLock;

use crate::config::Config;
use crate::memory::LocalMemory;

pub mod microbot;
pub mod meteora_damm;
pub mod arbitrage;

pub use microbot::MicroBotStrategy;
pub use meteora_damm::MeteoraDammStrategy;
pub use arbitrage::ArbitrageStrategy;

#[async_trait]
pub trait Strategy: Send + Sync {
    /// Execute one iteration of the strategy
    async fn execute(&self) -> Result<()>;
    
    /// Get strategy name
    fn name(&self) -> &str;
    
    /// Check if strategy should continue running
    async fn should_continue(&self) -> bool;
    
    /// Get current performance metrics
    async fn get_metrics(&self) -> StrategyMetrics;
}

#[derive(Debug, Clone)]
pub struct StrategyMetrics {
    pub total_trades: u64,
    pub successful_trades: u64,
    pub total_pnl_sol: f64,
    pub win_rate: f64,
    pub current_balance_sol: f64,
}

/// Common functionality for all strategies
pub struct BaseStrategy {
    pub config: Config,
    pub memory: Arc<RwLock<LocalMemory>>,
}

impl BaseStrategy {
    pub async fn log_trade(&self, trade: &TradeRecord) -> Result<()> {
        let mut memory = self.memory.write().await;
        memory.save_trade(trade).await?;
        Ok(())
    }
    
    pub async fn get_historical_performance(&self, token: &str) -> Result<Vec<TradeRecord>> {
        let memory = self.memory.read().await;
        memory.get_trades_for_token(token).await
    }
}

#[derive(Debug, Clone, serde::Serialize, serde::Deserialize)]
pub struct TradeRecord {
    pub id: String,
    pub timestamp: chrono::DateTime<chrono::Utc>,
    pub token_address: String,
    pub token_symbol: String,
    pub action: TradeAction,
    pub amount_sol: f64,
    pub price: f64,
    pub tx_signature: String,
    pub pnl_sol: Option<f64>,
    pub notes: Option<String>,
}

#[derive(Debug, Clone, serde::Serialize, serde::Deserialize)]
pub enum TradeAction {
    Buy,
    Sell,
    AddLiquidity,
    RemoveLiquidity,
}
