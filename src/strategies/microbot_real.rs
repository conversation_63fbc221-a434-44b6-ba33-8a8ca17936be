use async_trait::async_trait;
use anyhow::Result;
use chrono::Utc;
use solana_sdk::{
    pubkey::Pubkey,
    signature::Keypair,
    signer::Signer,
};
use solana_client::rpc_client::RpcClient;
use std::sync::Arc;
use tokio::sync::RwLock;
use tracing::{info, warn, debug, error};
use uuid::Uuid;
use rust_decimal::Decimal;
use std::str::FromStr;

use crate::config::Config;
use crate::memory::LocalMemory;
use crate::helius::{HeliusClient, assess_token_risk};
use crate::jupiter::{JupiterClient, QuoteRequest, SwapExecutor};
use crate::brightdata::{BrightdataClient, DataAggregator};
use crate::jito::{JitoBundleClient, MevStrategyManager};
use crate::ai::AiAnalyzer;
use crate::openmemory::{OpenMemoryClient, TokenMemory};
use super::{Strategy, StrategyMetrics, BaseStrategy, TradeRecord, TradeAction};

/// Token opportunity z pełną analizą
#[derive(Debug, Clone)]
pub struct TokenOpportunity {
    pub token_address: String,
    pub token_symbol: String,
    pub token_name: String,
    pub price: f64,
    pub liquidity_usd: f64,
    pub age_minutes: u64,
    pub risk_score: f64,
    pub risk_factors: Vec<String>,
    pub social_sentiment: f64,
    pub holder_count: u32,
    pub top_holder_percentage: f64,
    pub ai_recommendation: String,
}

/// MicroBot Strategy - Agresywny trading małym kapitałem (0.4 SOL)
pub struct MicroBotStrategy {
    base: BaseStrategy,
    current_balance: Arc<RwLock<f64>>,
    total_trades: Arc<RwLock<u64>>,
    successful_trades: Arc<RwLock<u64>>,
    
    // Klienty zewnętrzne
    rpc_client: Arc<RpcClient>,
    helius_client: Arc<HeliusClient>,
    jupiter_client: Arc<JupiterClient>,
    jito_client: Arc<JitoBundleClient>,
    brightdata_aggregator: Arc<RwLock<DataAggregator>>,
    ai_analyzer: Arc<AiAnalyzer>,
    openmemory: Arc<OpenMemoryClient>,
    
    // Wallet
    wallet: Arc<Keypair>,
    
    // Cache tokenów do obserwacji
    watchlist: Arc<RwLock<Vec<TokenOpportunity>>>,
}

impl MicroBotStrategy {
    pub fn new(
        config: Config, 
        memory: Arc<RwLock<LocalMemory>>,
        rpc_client: Arc<RpcClient>,
        wallet: Arc<Keypair>,
    ) -> Result<Self> {
        let initial_balance = config.microbot.initial_capital_sol;
        
        // Inicjalizacja klientów
        let helius_api_key = std::env::var("HELIUS_API_KEY")
            .unwrap_or_else(|_| "demo_key".to_string());
        let brightdata_api_key = std::env::var("BRIGHTDATA_API_KEY")
            .unwrap_or_else(|_| "demo_key".to_string());
        
        let helius_client = Arc::new(HeliusClient::new(helius_api_key));
        let jupiter_client = Arc::new(JupiterClient::new());
        let jito_client = Arc::new(JitoBundleClient::new("https://mainnet.block-engine.jito.wtf"));
        let brightdata_aggregator = Arc::new(RwLock::new(DataAggregator::new(brightdata_api_key)?));
        let ai_analyzer = Arc::new(AiAnalyzer::new(&config.ai.sentiment_api_url));
        let openmemory = Arc::new(OpenMemoryClient::new(
            &config.openmemory.base_url,
            &config.openmemory.user_id,
        ));
        
        Ok(Self {
            base: BaseStrategy { config, memory },
            current_balance: Arc::new(RwLock::new(initial_balance)),
            total_trades: Arc::new(RwLock::new(0)),
            successful_trades: Arc::new(RwLock::new(0)),
            rpc_client,
            helius_client,
            jupiter_client,
            jito_client,
            brightdata_aggregator,
            ai_analyzer,
            openmemory,
            wallet,
            watchlist: Arc::new(RwLock::new(vec![])),
        })
    }
    
    /// Skanuje blockchain i źródła zewnętrzne w poszukiwaniu okazji
    async fn find_opportunities(&self) -> Result<Vec<TokenOpportunity>> {
        info!("🔍 Scanning for new meme tokens...");
        
        let mut opportunities = vec![];
        
        // 1. Monitoruj pump.fun przez Brightdata
        let mut brightdata = self.brightdata_aggregator.write().await;
        let new_listings = brightdata.brightdata_client.monitor_pump_fun().await?;
        
        for listing in new_listings {
            debug!("Found new listing: {} ({})", listing.token_symbol, listing.token_name);
            
            // 2. Analiza ryzyka przez Helius
            let token_pubkey = Pubkey::from_str(&listing.token_address)?;
            let risk_assessment = assess_token_risk(&self.helius_client, &token_pubkey).await?;
            
            if risk_assessment.risk_score > 0.7 {
                warn!("⚠️ High risk token {}: {:?}", 
                    listing.token_symbol, risk_assessment.risk_factors);
                continue;
            }
            
            // 3. Analiza społecznościowa
            let social_data = brightdata
                .get_token_data(&listing.token_address, &listing.token_symbol, 5)
                .await?;
            
            // 4. Analiza AI sentymentu
            let ai_sentiment = self.ai_analyzer
                .analyze_token_sentiment(&listing.token_address, &listing.token_symbol)
                .await?;
            
            // 5. Sprawdź podobne tokeny w pamięci
            let similar_tokens = self.openmemory
                .find_similar_tokens(&listing.token_address, 5)
                .await?;
            
            let mut ai_recommendation = ai_sentiment.overall_sentiment.clone();
            if !similar_tokens.is_empty() {
                debug!("Found {} similar tokens in memory", similar_tokens.len());
                // Dostosuj rekomendację na podstawie historii
                for memory in &similar_tokens {
                    if memory.metadata.get("outcome").and_then(|v| v.as_str()) == Some("rugpull") {
                        ai_recommendation = "avoid".to_string();
                        break;
                    }
                }
            }
            
            // Buduj okazję
            let opportunity = TokenOpportunity {
                token_address: listing.token_address,
                token_symbol: listing.token_symbol,
                token_name: listing.token_name,
                price: 0.0000001, // Pobierz rzeczywistą cenę z DEX
                liquidity_usd: listing.initial_liquidity,
                age_minutes: (Utc::now() - listing.launch_time).num_minutes() as u64,
                risk_score: risk_assessment.risk_score,
                risk_factors: risk_assessment.risk_factors,
                social_sentiment: social_data.sentiment_score,
                holder_count: risk_assessment.holder_distribution.total_holders,
                top_holder_percentage: risk_assessment.holder_distribution.top_holders
                    .first()
                    .map(|h| h.percentage)
                    .unwrap_or(0.0),
                ai_recommendation,
            };
            
            opportunities.push(opportunity);
        }
        
        // 6. Monitoruj również istniejące tokeny z watchlisty
        let watchlist = self.watchlist.read().await.clone();
        for watched in watchlist {
            // Aktualizuj dane
            if let Ok(updated) = self.update_token_data(&watched).await {
                opportunities.push(updated);
            }
        }
        
        Ok(opportunities)
    }
    
    /// Aktualizuje dane o tokenie
    async fn update_token_data(&self, token: &TokenOpportunity) -> Result<TokenOpportunity> {
        // Pobierz świeże dane cenowe z Jupiter
        let sol_mint = "So11111111111111111111111111111111111111112";
        let quote_request = QuoteRequest {
            input_mint: sol_mint.to_string(),
            output_mint: token.token_address.clone(),
            amount: "1000000000".to_string(), // 1 SOL
            slippage_bps: 100,
            only_direct_routes: Some(false),
            as_legacy_transaction: Some(false),
        };
        
        let quote = self.jupiter_client.get_quote(quote_request).await?;
        let price = 1.0 / (Decimal::from_str(&quote.out_amount)? / Decimal::from(10u64.pow(9))).to_f64().unwrap();
        
        let mut updated = token.clone();
        updated.price = price;
        updated.age_minutes = token.age_minutes + 1;
        
        Ok(updated)
    }
    
    /// Wykonuje rzeczywistą transakcję zakupu
    async fn execute_trade(&self, opportunity: &TokenOpportunity) -> Result<bool> {
        let balance = *self.current_balance.read().await;
        let position_size = balance * (self.base.config.microbot.position_size_percent / 100.0);
        
        info!(
            "💰 Executing trade: {} ({}) - Position: {} SOL",
            opportunity.token_symbol,
            &opportunity.token_address[..8],
            position_size
        );
        
        // Przygotuj transakcję swap
        let sol_mint = Pubkey::from_str("So11111111111111111111111111111111111111112")?;
        let token_mint = Pubkey::from_str(&opportunity.token_address)?;
        let amount_lamports = (position_size * 1e9) as u64;
        
        // Oblicz optymalny slippage
        let slippage = self.jupiter_client.calculate_optimal_slippage(
            position_size,
            opportunity.liquidity_usd,
            true, // is_buy
        );
        
        // Pobierz quote
        let quote_request = QuoteRequest {
            input_mint: sol_mint.to_string(),
            output_mint: token_mint.to_string(),
            amount: amount_lamports.to_string(),
            slippage_bps: slippage,
            only_direct_routes: Some(false),
            as_legacy_transaction: Some(false),
        };
        
        let quote = self.jupiter_client.get_quote(quote_request).await?;
        
        // Sprawdź opłacalność
        let gas_estimate = 5000; // 0.000005 SOL
        if !self.jupiter_client.is_swap_profitable(&quote, gas_estimate, 0.001)? {
            warn!("Trade not profitable after fees");
            return Ok(false);
        }
        
        // Pobierz transakcję
        let priority_fee = if self.base.config.trading.use_jito { 100_000 } else { 10_000 };
        let swap_response = self.jupiter_client
            .get_swap_transaction(quote.clone(), &self.wallet.pubkey(), Some(priority_fee))
            .await?;
        
        // Deserializuj i podpisz
        let tx_bytes = base64::decode(&swap_response.swap_transaction)?;
        let mut tx: solana_sdk::transaction::Transaction = bincode::deserialize(&tx_bytes)?;
        tx.sign(&[&*self.wallet], tx.message.recent_blockhash);
        
        // Wyślij przez Jito jeśli włączone
        let signature = if self.base.config.trading.use_jito {
            let tip_lamports = self.jito_client
                .optimize_tip_amount(0.01, 0.5, 0.6)
                .await;
            
            let bundle_response = self.jito_client
                .submit_bundle(vec![tx], tip_lamports, &self.wallet)
                .await?;
            
            info!("📦 Sent via Jito bundle: {}", bundle_response.bundle_id);
            bundle_response.bundle_id
        } else {
            // Wyślij bezpośrednio
            let signature = self.rpc_client
                .send_and_confirm_transaction(&tx)?;
            signature.to_string()
        };
        
        info!("✅ Trade executed: {}", signature);
        
        // Zapisz w pamięci
        let trade = TradeRecord {
            id: Uuid::new_v4().to_string(),
            timestamp: Utc::now(),
            token_address: opportunity.token_address.clone(),
            token_symbol: opportunity.token_symbol.clone(),
            action: TradeAction::Buy,
            amount_sol: position_size,
            price: opportunity.price,
            tx_signature: signature,
            pnl_sol: None, // Będzie obliczone przy sprzedaży
            notes: Some(format!(
                "Risk: {:.2}, Social: {:.2}, AI: {}",
                opportunity.risk_score,
                opportunity.social_sentiment,
                opportunity.ai_recommendation
            )),
        };
        
        self.base.log_trade(&trade).await?;
        
        // Zapisz w OpenMemory
        let token_memory = TokenMemory {
            token_address: opportunity.token_address.clone(),
            token_name: opportunity.token_name.clone(),
            token_symbol: opportunity.token_symbol.clone(),
            initial_price: opportunity.price,
            initial_liquidity: opportunity.liquidity_usd,
            creator_address: "unknown".to_string(), // TODO: pobierz z metadanych
            contract_features: opportunity.risk_factors.clone(),
            outcome: "pending".to_string(),
            profit_percentage: 0.0,
            timestamp: Utc::now(),
            ttl: 15_552_000, // 180 dni
        };
        
        self.openmemory.add_token_memory(&token_memory).await?;
        
        // Aktualizuj statystyki
        *self.total_trades.write().await += 1;
        
        // Dodaj do watchlisty do monitorowania
        self.watchlist.write().await.push(opportunity.clone());
        
        Ok(true)
    }
}

#[async_trait]
impl Strategy for MicroBotStrategy {
    async fn execute(&self) -> Result<()> {
        debug!("🔄 MicroBot strategy iteration");
        
        // Znajdź okazje
        let opportunities = self.find_opportunities().await?;
        info!("Found {} potential opportunities", opportunities.len());
        
        // Posortuj według potencjału (niska ocena ryzyka + wysoki sentyment)
        let mut sorted_opportunities = opportunities;
        sorted_opportunities.sort_by(|a, b| {
            let a_score = (1.0 - a.risk_score) * a.social_sentiment;
            let b_score = (1.0 - b.risk_score) * b.social_sentiment;
            b_score.partial_cmp(&a_score).unwrap()
        });
        
        // Wykonaj najlepszą okazję
        for opportunity in sorted_opportunities.iter().take(3) {
            // Sprawdź kryteria
            if opportunity.risk_score < self.base.config.microbot.max_risk_score &&
               opportunity.liquidity_usd >= self.base.config.microbot.min_liquidity_usd &&
               opportunity.age_minutes <= self.base.config.microbot.max_token_age_minutes &&
               opportunity.ai_recommendation != "avoid" {
                
                info!("✨ Executing opportunity: {} (Risk: {:.2}, Sentiment: {:.2})",
                    opportunity.token_symbol,
                    opportunity.risk_score,
                    opportunity.social_sentiment
                );
                
                match self.execute_trade(&opportunity).await {
                    Ok(success) => {
                        if success {
                            info!("✅ Trade successful!");
                            break; // Wykonaj tylko jedną transakcję na iterację
                        }
                    }
                    Err(e) => {
                        error!("⚠️ Trade execution error: {}", e);
                    }
                }
            }
        }
        
        // Monitoruj istniejące pozycje
        self.monitor_positions().await?;
        
        // Cleanup
        self.brightdata_aggregator.write().await.cleanup_cache(60);
        
        // Pauza między iteracjami
        tokio::time::sleep(tokio::time::Duration::from_secs(10)).await;
        
        Ok(())
    }
    
    fn name(&self) -> &str {
        "MicroBot Strategy (Real)"
    }
    
    async fn should_continue(&self) -> bool {
        let balance = *self.current_balance.read().await;
        
        // Kontynuuj jeśli mamy więcej niż 0.1 SOL
        if balance < 0.1 {
            warn!("💸 Balance too low: {} SOL. Stopping.", balance);
            false
        } else {
            true
        }
    }
    
    async fn get_metrics(&self) -> StrategyMetrics {
        let total = *self.total_trades.read().await;
        let successful = *self.successful_trades.read().await;
        let balance = *self.current_balance.read().await;
        
        StrategyMetrics {
            total_trades: total,
            successful_trades: successful,
            total_pnl_sol: balance - self.base.config.microbot.initial_capital_sol,
            win_rate: if total > 0 { 
                (successful as f64 / total as f64) * 100.0 
            } else { 
                0.0 
            },
            current_balance_sol: balance,
        }
    }
}

impl MicroBotStrategy {
    /// Monitoruje otwarte pozycje i decyduje o sprzedaży
    async fn monitor_positions(&self) -> Result<()> {
        let watchlist = self.watchlist.read().await.clone();
        
        for token in watchlist {
            // Pobierz aktualną cenę
            if let Ok(updated) = self.update_token_data(&token).await {
                let price_change = (updated.price - token.price) / token.price;
                
                // Strategia wyjścia
                let should_sell = 
                    price_change >= self.base.config.microbot.take_profit_percent / 100.0 || // Take profit
                    price_change <= -(self.base.config.microbot.stop_loss_percent / 100.0) || // Stop loss
                    updated.age_minutes > 60; // Maksymalny czas trzymania
                
                if should_sell {
                    info!("📈 Selling {} - Price change: {:.1}%", 
                        token.token_symbol, 
                        price_change * 100.0
                    );
                    
                    // TODO: Wykonaj transakcję sprzedaży
                    
                    // Aktualizuj statystyki
                    if price_change > 0.0 {
                        *self.successful_trades.write().await += 1;
                    }
                    
                    // Usuń z watchlisty
                    self.watchlist.write().await.retain(|t| t.token_address != token.token_address);
                }
            }
        }
        
        Ok(())
    }
}
