use async_trait::async_trait;
use anyhow::Result;
use chrono::Utc;
use solana_sdk::pubkey::Pubkey;
use std::sync::Arc;
use tokio::sync::RwLock;
use tracing::{info, warn, debug};
use uuid::Uuid;

use crate::config::Config;
use crate::memory::LocalMemory;
use super::{Strategy, StrategyMetrics, BaseStrategy, TradeRecord, TradeAction};

/// MicroBot Strategy - Agresywny trading małym kapitałem (0.4 SOL)
pub struct MicroBotStrategy {
    base: BaseStrategy,
    current_balance: Arc<RwLock<f64>>,
    total_trades: Arc<RwLock<u64>>,
    successful_trades: Arc<RwLock<u64>>,
}

impl MicroBotStrategy {
    pub fn new(config: Config, memory: Arc<RwLock<LocalMemory>>) -> Self {
        let initial_balance = config.microbot.initial_capital_sol;
        
        Self {
            base: BaseStrategy { config, memory },
            current_balance: Arc::new(RwLock::new(initial_balance)),
            total_trades: Arc::new(RwLock::new(0)),
            successful_trades: Arc::new(RwLock::new(0)),
        }
    }
    
    async fn find_opportunity(&self) -> Result<Option<TokenOpportunity>> {
        info!("🔍 Scanning for new meme tokens...");
        
        // W prawdziwej implementacji:
        // 1. Skanuj Raydium/Orca/Pump.fun za nowymi tokenami
        // 2. Filtruj po wieku (< 5 minut)
        // 3. Sprawdź płynność (> $1000)
        // 4. Analizuj ryzyko (brak freeze/mint authority)
        
        // Symulacja dla demo
        if rand::random::<f64>() > 0.7 {
            Ok(Some(TokenOpportunity {
                token_address: Pubkey::new_unique().to_string(),
                token_symbol: format!("MEME{}", rand::random::<u32>() % 1000),
                price: 0.0000001 * (1.0 + rand::random::<f64>()),
                liquidity_usd: 1000.0 + rand::random::<f64>() * 9000.0,
                age_minutes: rand::random::<u64>() % 5,
                risk_score: rand::random::<f64>() * 0.5, // 0-0.5 dla demo
            }))
        } else {
            Ok(None)
        }
    }
    
    async fn execute_trade(&self, opportunity: &TokenOpportunity) -> Result<bool> {
        let balance = *self.current_balance.read().await;
        let position_size = balance * (self.base.config.microbot.position_size_percent / 100.0);
        
        info!(
            "💰 Executing trade: {} ({}) - Position: {} SOL",
            opportunity.token_symbol,
            &opportunity.token_address[..8],
            position_size
        );
        
        // W prawdziwej implementacji:
        // 1. Zbuduj transakcję swap przez Jupiter
        // 2. Ustaw slippage i priority fee
        // 3. Wyślij transakcję
        // 4. Czekaj na potwierdzenie
        
        // Symulacja wyniku
        let success = rand::random::<f64>() > 0.3; // 70% success rate
        let pnl_multiplier = if success {
            1.0 + rand::random::<f64>() * 0.5 // 0-50% profit
        } else {
            0.9 - rand::random::<f64>() * 0.1 // 0-10% loss
        };
        
        // Aktualizuj balans
        let mut balance_lock = self.current_balance.write().await;
        let new_balance = (*balance_lock - position_size) + (position_size * pnl_multiplier);
        *balance_lock = new_balance;
        
        // Zapisz trade
        let trade = TradeRecord {
            id: Uuid::new_v4().to_string(),
            timestamp: Utc::now(),
            token_address: opportunity.token_address.clone(),
            token_symbol: opportunity.token_symbol.clone(),
            action: TradeAction::Buy,
            amount_sol: position_size,
            price: opportunity.price,
            tx_signature: format!("sim_{}", Uuid::new_v4()),
            pnl_sol: Some(position_size * (pnl_multiplier - 1.0)),
            notes: Some(format!("Risk score: {:.2}", opportunity.risk_score)),
        };
        
        self.base.log_trade(&trade).await?;
        
        // Aktualizuj statystyki
        *self.total_trades.write().await += 1;
        if success {
            *self.successful_trades.write().await += 1;
        }
        
        Ok(success)
    }
    
    async fn apply_dynamic_position_sizing(&self) -> f64 {
        let balance = *self.current_balance.read().await;
        
        // Dynamiczne dostosowanie wielkości pozycji
        match balance {
            b if b <= 1.0 => 80.0,   // Agresywne przy małym kapitale
            b if b <= 5.0 => 60.0,   // Umiarkowane
            b if b <= 20.0 => 40.0,  // Konserwatywne
            _ => 20.0,               // Bardzo bezpieczne
        }
    }
}

#[async_trait]
impl Strategy for MicroBotStrategy {
    async fn execute(&self) -> Result<()> {
        debug!("🔄 MicroBot strategy iteration");
        
        // Znajdź okazję
        if let Some(opportunity) = self.find_opportunity().await? {
            info!("✨ Found opportunity: {} (Risk: {:.2})", 
                opportunity.token_symbol, 
                opportunity.risk_score
            );
            
            // Sprawdź kryteria
            if opportunity.risk_score < 0.3 && 
               opportunity.liquidity_usd >= self.base.config.microbot.min_liquidity_usd &&
               opportunity.age_minutes <= self.base.config.microbot.max_token_age_minutes {
                
                // Wykonaj trade
                match self.execute_trade(&opportunity).await {
                    Ok(success) => {
                        if success {
                            info!("✅ Trade successful!");
                        } else {
                            warn!("❌ Trade failed - stop loss hit");
                        }
                    }
                    Err(e) => {
                        warn!("⚠️ Trade execution error: {}", e);
                    }
                }
            } else {
                debug!("⏭️ Skipping opportunity - criteria not met");
            }
        }
        
        // Pauza między iteracjami
        tokio::time::sleep(tokio::time::Duration::from_secs(5)).await;
        
        Ok(())
    }
    
    fn name(&self) -> &str {
        "MicroBot Strategy"
    }
    
    async fn should_continue(&self) -> bool {
        let balance = *self.current_balance.read().await;
        
        // Kontynuuj jeśli mamy więcej niż 0.1 SOL
        if balance < 0.1 {
            warn!("💸 Balance too low: {} SOL. Stopping.", balance);
            false
        } else {
            true
        }
    }
    
    async fn get_metrics(&self) -> StrategyMetrics {
        let total = *self.total_trades.read().await;
        let successful = *self.successful_trades.read().await;
        let balance = *self.current_balance.read().await;
        
        StrategyMetrics {
            total_trades: total,
            successful_trades: successful,
            total_pnl_sol: balance - self.base.config.microbot.initial_capital_sol,
            win_rate: if total > 0 { 
                (successful as f64 / total as f64) * 100.0 
            } else { 
                0.0 
            },
            current_balance_sol: balance,
        }
    }
}

#[derive(Debug, Clone)]
struct TokenOpportunity {
    token_address: String,
    token_symbol: String,
    price: f64,
    liquidity_usd: f64,
    age_minutes: u64,
    risk_score: f64,
}
