use anyhow::Result;
use serde::{Deserialize, Serialize};
use std::fs;

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct Config {
    pub strategy: String,
    pub wallet: WalletConfig,
    pub rpc: RpcConfig,
    pub trading: TradingConfig,
    pub microbot: MicroBotConfig,
    pub meteora: MeteoraConfig,
}

#[derive(<PERSON>bu<PERSON>, <PERSON>lone, Serialize, Deserialize)]
pub struct WalletConfig {
    pub private_key_path: String,
    pub use_env_key: bool,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct RpcConfig {
    pub endpoints: Vec<String>,
    pub helius_api_key: Option<String>,
    pub websocket_url: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TradingConfig {
    pub slippage_bps: u16,
    pub priority_fee_lamports: u64,
    pub dry_run: bool,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct MicroBotConfig {
    pub initial_capital_sol: f64,
    pub position_size_percent: f64,
    pub stop_loss_percent: f64,
    pub take_profit_targets: Vec<f64>,
    pub max_token_age_minutes: u64,
    pub min_liquidity_usd: f64,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MeteoraConfig {
    pub min_pool_liquidity_usd: f64,
    pub max_initial_fee_bps: u16,
    pub position_size_usd: f64,
    pub max_impermanent_loss_percent: f64,
    pub compound_threshold_usd: f64,
}

impl Config {
    pub fn load() -> Result<Self> {
        // Try to load from config.toml, otherwise use defaults
        if let Ok(contents) = fs::read_to_string("config.toml") {
            Ok(toml::from_str(&contents)?)
        } else {
            Ok(Self::default())
        }
    }
}

impl Default for Config {
    fn default() -> Self {
        Self {
            strategy: "microbot".to_string(),
            wallet: WalletConfig {
                private_key_path: "./.keys/wallet.json".to_string(),
                use_env_key: true,
            },
            rpc: RpcConfig {
                endpoints: vec![
                    "https://api.mainnet-beta.solana.com".to_string(),
                ],
                helius_api_key: None,
                websocket_url: None,
            },
            trading: TradingConfig {
                slippage_bps: 500, // 5%
                priority_fee_lamports: 5000,
                dry_run: true, // Safety first!
            },
            microbot: MicroBotConfig {
                initial_capital_sol: 0.4,
                position_size_percent: 80.0,
                stop_loss_percent: 10.0,
                take_profit_targets: vec![50.0, 100.0, 200.0],
                max_token_age_minutes: 5,
                min_liquidity_usd: 1000.0,
            },
            meteora: MeteoraConfig {
                min_pool_liquidity_usd: 10000.0,
                max_initial_fee_bps: 1000, // 10%
                position_size_usd: 500.0,
                max_impermanent_loss_percent: 20.0,
                compound_threshold_usd: 50.0,
            },
        }
    }
}
