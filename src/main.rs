use anyhow::Result;
use solana_sdk::pubkey::Pubkey;
use std::sync::Arc;
use tokio::sync::RwLock;
use tracing::{info, warn};

mod config;
mod core;
mod strategies;
mod memory;
mod api;
mod helius;
mod jupiter;
mod jito;
mod ai;
mod brightdata;
mod openmemory;

use config::Config;
use core::sniper_bot::SniperBot;
use strategies::{Strategy, MicroBotStrategy, MeteoraDammStrategy};
use memory::LocalMemory;

#[tokio::main]
async fn main() -> Result<()> {
    // Initialize logging
    tracing_subscriber::fmt()
        .with_env_filter("info")
        .with_target(false)
        .init();

    info!("🎯 Starting SniperBot...");

    // Load configuration
    let config = Config::load()?;
    info!("📋 Loaded configuration");

    // Initialize local memory (lightweight alternative to OpenMemory)
    let memory = Arc::new(RwLock::new(LocalMemory::new("./data/memory.db").await?));
    info!("🧠 Initialized local memory");

    // Create bot instance
    let bot = Arc::new(SniperBot::new(config.clone(), memory.clone()).await?);
    info!("🤖 Bot initialized");

    // Select strategy based on config
    let strategy: Box<dyn Strategy> = match config.strategy.as_str() {
        "microbot" => {
            info!("💎 Using MicroBot strategy (0.4 SOL capital)");
            Box::new(MicroBotStrategy::new(config.clone(), memory.clone()))
        }
        "meteora" => {
            info!("🌊 Using Meteora DAMM strategy");
            Box::new(MeteoraDammStrategy::new(config.clone(), memory.clone()))
        }
        _ => {
            warn!("Unknown strategy, defaulting to MicroBot");
            Box::new(MicroBotStrategy::new(config.clone(), memory.clone()))
        }
    };

    // Start API server in background
    let api_bot = bot.clone();
    tokio::spawn(async move {
        if let Err(e) = api::start_server(api_bot).await {
            warn!("API server error: {}", e);
        }
    });

    // Main trading loop
    info!("🚀 Starting main trading loop");
    bot.run(strategy).await?;

    Ok(())
}
