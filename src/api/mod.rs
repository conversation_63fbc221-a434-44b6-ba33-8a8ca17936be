use axum::{
    extract::{State, Query},
    http::StatusCode,
    response::Json,
    routing::{get, post},
    Router,
};
use serde::{Deserialize, Serialize};
use std::sync::Arc;
use tower_http::cors::CorsLayer;
use tracing::info;

use crate::core::sniper_bot::SniperBot;

pub async fn start_server(bot: Arc<SniperBot>) -> anyhow::Result<()> {
    let app = Router::new()
        .route("/health", get(health_check))
        .route("/metrics", get(get_metrics))
        .route("/status", get(get_status))
        .route("/stop", post(stop_bot))
        .layer(CorsLayer::permissive())
        .with_state(bot);

    let addr = "0.0.0.0:8080";
    info!("🌐 API server listening on {}", addr);
    
    let listener = tokio::net::TcpListener::bind(addr).await?;
    axum::serve(listener, app).await?;
    
    Ok(())
}

async fn health_check() -> &'static str {
    "OK"
}

async fn get_metrics(State(bot): State<Arc<SniperBot>>) -> <PERSON>son<MetricsResponse> {
    // In real implementation, would get metrics from bot
    Json(MetricsResponse {
        status: "running".to_string(),
        total_trades: 0,
        win_rate: 0.0,
        total_pnl_sol: 0.0,
        current_balance_sol: 0.0,
        uptime_seconds: 0,
    })
}

async fn get_status(State(bot): State<Arc<SniperBot>>) -> Json<StatusResponse> {
    Json(StatusResponse {
        bot_status: "active".to_string(),
        current_strategy: "microbot".to_string(),
        wallet_address: bot.get_wallet().pubkey().to_string(),
        rpc_endpoint: "mainnet-beta".to_string(),
        dry_run_mode: true,
    })
}

async fn stop_bot(State(bot): State<Arc<SniperBot>>) -> StatusCode {
    bot.stop().await;
    StatusCode::OK
}

#[derive(Serialize)]
struct MetricsResponse {
    status: String,
    total_trades: u64,
    win_rate: f64,
    total_pnl_sol: f64,
    current_balance_sol: f64,
    uptime_seconds: u64,
}

#[derive(Serialize)]
struct StatusResponse {
    bot_status: String,
    current_strategy: String,
    wallet_address: String,
    rpc_endpoint: String,
    dry_run_mode: bool,
}
