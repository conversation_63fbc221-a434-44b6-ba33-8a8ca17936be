use anyhow::Result;
use base64::Engine;
use reqwest::Client;
use serde::{Deserialize, Serialize};
use solana_sdk::{
    instruction::Instruction,
    pubkey::Pubkey,
    transaction::Transaction,
    signature::Keypair,
    signer::Signer,
};
use rust_decimal::Decimal;
use std::str::FromStr;
use tracing::{info, debug, warn};

/// Jupiter Aggregator Client - optymalne swapy na Solanie
pub struct JupiterClient {
    client: Client,
    base_url: String,
}

#[derive(Debug, Serialize)]
#[serde(rename_all = "camelCase")]
pub struct QuoteRequest {
    pub input_mint: String,
    pub output_mint: String,
    pub amount: String,
    pub slippage_bps: u32,
    pub only_direct_routes: Option<bool>,
    pub as_legacy_transaction: Option<bool>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct QuoteResponse {
    pub input_mint: String,
    pub in_amount: String,
    pub output_mint: String,
    pub out_amount: String,
    pub other_amount_threshold: String,
    pub swap_mode: String,
    pub slippage_bps: u32,
    pub platform_fee: Option<PlatformFee>,
    pub price_impact_pct: String,
    pub route_plan: Vec<RoutePlanStep>,
    pub context_slot: Option<u64>,
    pub time_taken: Option<f64>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct PlatformFee {
    pub amount: String,
    pub fee_bps: u32,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct RoutePlanStep {
    pub swap_info: SwapInfo,
    pub percent: u8,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct SwapInfo {
    pub amm_key: String,
    pub label: String,
    pub input_mint: String,
    pub output_mint: String,
    pub in_amount: String,
    pub out_amount: String,
    pub fee_amount: String,
    pub fee_mint: String,
}

#[derive(Debug, Serialize)]
#[serde(rename_all = "camelCase")]
pub struct SwapRequest {
    pub user_public_key: String,
    pub wrap_and_unwrap_sol: Option<bool>,
    pub use_shared_accounts: Option<bool>,
    pub fee_account: Option<String>,
    pub compute_unit_price_micro_lamports: Option<u64>,
    pub as_legacy_transaction: Option<bool>,
    pub use_token_ledger: Option<bool>,
    pub destination_token_account: Option<String>,
    pub quote_response: QuoteResponse,
}

#[derive(Debug, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct SwapResponse {
    pub swap_transaction: String,
    pub last_valid_block_height: u64,
    pub priority_fee_lamports: Option<u64>,
}

#[derive(Debug, Serialize)]
pub struct SwapInstructionsRequest {
    pub user_public_key: String,
    pub quote_response: QuoteResponse,
}

#[derive(Debug, Deserialize)]
pub struct SwapInstructionsResponse {
    pub token_ledger_instruction: Option<InstructionData>,
    pub compute_budget_instructions: Vec<InstructionData>,
    pub setup_instructions: Vec<InstructionData>,
    pub swap_instruction: InstructionData,
    pub cleanup_instruction: Option<InstructionData>,
    pub other_instructions: Vec<InstructionData>,
}

#[derive(Debug, Deserialize)]
pub struct InstructionData {
    pub program_id: String,
    pub accounts: Vec<AccountMeta>,
    pub data: String,
}

#[derive(Debug, Deserialize)]
pub struct AccountMeta {
    pub pubkey: String,
    pub is_signer: bool,
    pub is_writable: bool,
}

impl JupiterClient {
    pub fn new() -> Self {
        info!("🃏 Initializing Jupiter Aggregator Client");
        Self {
            client: Client::new(),
            base_url: "https://quote-api.jup.ag/v6".to_string(),
        }
    }

    /// Pobiera quote dla swapa
    pub async fn get_quote(&self, request: QuoteRequest) -> Result<QuoteResponse> {
        debug!("Getting quote: {} -> {}", request.input_mint, request.output_mint);

        let response = self.client
            .get(&format!("{}/quote", self.base_url))
            .query(&[
                ("inputMint", request.input_mint.as_str()),
                ("outputMint", request.output_mint.as_str()),
                ("amount", request.amount.as_str()),
                ("slippageBps", &request.slippage_bps.to_string()),
            ])
            .send()
            .await?;

        if response.status().is_success() {
            let quote: QuoteResponse = response.json().await?;

            let price_impact = Decimal::from_str(&quote.price_impact_pct)?;
            info!(
                "📊 Quote received: {} {} -> {} {} (impact: {}%)",
                Decimal::from_str(&quote.in_amount)? / Decimal::from(10u64.pow(9)),
                &quote.input_mint[..8],
                Decimal::from_str(&quote.out_amount)? / Decimal::from(10u64.pow(9)),
                &quote.output_mint[..8],
                price_impact
            );

            Ok(quote)
        } else {
            let error_text = response.text().await?;
            Err(anyhow::anyhow!("Failed to get quote: {}", error_text))
        }
    }

    /// Pobiera gotową transakcję swap
    pub async fn get_swap_transaction(
        &self,
        quote: QuoteResponse,
        user_public_key: &Pubkey,
        priority_fee: Option<u64>,
    ) -> Result<SwapResponse> {
        debug!("Building swap transaction for {}", user_public_key);

        let request = SwapRequest {
            user_public_key: user_public_key.to_string(),
            wrap_and_unwrap_sol: Some(true),
            use_shared_accounts: Some(true),
            fee_account: None,
            compute_unit_price_micro_lamports: priority_fee,
            as_legacy_transaction: Some(false),
            use_token_ledger: Some(false),
            destination_token_account: None,
            quote_response: quote,
        };

        let response = self.client
            .post(&format!("{}/swap", self.base_url))
            .json(&request)
            .send()
            .await?;

        if response.status().is_success() {
            let swap_response: SwapResponse = response.json().await?;
            info!("✅ Swap transaction ready, priority fee: {:?}", swap_response.priority_fee_lamports);
            Ok(swap_response)
        } else {
            let error_text = response.text().await?;
            Err(anyhow::anyhow!("Failed to get swap transaction: {}", error_text))
        }
    }

    /// Pobiera instrukcje swap (dla bardziej zaawansowanego użycia)
    pub async fn get_swap_instructions(
        &self,
        quote: QuoteResponse,
        user_public_key: &Pubkey,
    ) -> Result<SwapInstructionsResponse> {
        let request = SwapInstructionsRequest {
            user_public_key: user_public_key.to_string(),
            quote_response: quote,
        };

        let response = self.client
            .post(&format!("{}/swap-instructions", self.base_url))
            .json(&request)
            .send()
            .await?;

        if response.status().is_success() {
            Ok(response.json().await?)
        } else {
            let error_text = response.text().await?;
            Err(anyhow::anyhow!("Failed to get swap instructions: {}", error_text))
        }
    }

    /// Oblicza optymalny slippage na podstawie płynności i wielkości transakcji
    pub fn calculate_optimal_slippage(
        &self,
        amount_sol: f64,
        liquidity_usd: f64,
        is_buy: bool,
    ) -> u32 {
        // Bazowy slippage
        let mut slippage_bps = 100; // 1%

        // Dostosuj do rozmiaru transakcji względem płynności
        let trade_impact = (amount_sol * 200.0) / liquidity_usd; // Zakładamy $200/SOL

        if trade_impact > 0.1 {
            // Duża transakcja względem płynności
            slippage_bps = 300; // 3%
        } else if trade_impact > 0.05 {
            slippage_bps = 200; // 2%
        }

        // Dodatkowy slippage dla kupna (większa presja cenowa)
        if is_buy {
            slippage_bps += 50;
        }

        // Maksymalnie 5%
        slippage_bps.min(500)
    }

    /// Weryfikuje czy swap jest opłacalny po uwzględnieniu wszystkich kosztów
    pub fn is_swap_profitable(
        &self,
        quote: &QuoteResponse,
        gas_estimate: u64,
        min_profit_sol: f64,
    ) -> Result<bool> {
        let in_amount = Decimal::from_str(&quote.in_amount)?;
        let out_amount = Decimal::from_str(&quote.out_amount)?;
        let price_impact = Decimal::from_str(&quote.price_impact_pct)?;

        // Oblicz rzeczywisty koszt (input + gas)
        let total_cost = in_amount + Decimal::from(gas_estimate);

        // Oblicz oczekiwany zysk (uwzględnij price impact)
        let expected_value = out_amount * (Decimal::from(1) - price_impact / Decimal::from(100));

        // Sprawdź czy zysk przekracza próg
        let profit = expected_value - total_cost;
        let profit_sol = profit / Decimal::from(10u64.pow(9));

        debug!(
            "Swap profitability: cost={}, expected={}, profit={} SOL",
            total_cost / Decimal::from(10u64.pow(9)),
            expected_value / Decimal::from(10u64.pow(9)),
            profit_sol
        );

        Ok(profit_sol > Decimal::from_f64_retain(min_profit_sol).unwrap())
    }
}

/// Helper do wykonywania swapów
pub struct SwapExecutor {
    jupiter_client: JupiterClient,
    default_slippage_bps: u32,
}

impl SwapExecutor {
    pub fn new() -> Self {
        Self {
            jupiter_client: JupiterClient::new(),
            default_slippage_bps: 100, // 1%
        }
    }

    /// Wykonuje swap z automatycznym retry
    pub async fn execute_swap_with_retry(
        &self,
        input_mint: &Pubkey,
        output_mint: &Pubkey,
        amount: u64,
        wallet: &Keypair,
        max_retries: u32,
    ) -> Result<String> {
        let mut retries = 0;
        let mut last_error = None;

        while retries < max_retries {
            match self.execute_swap(input_mint, output_mint, amount, wallet).await {
                Ok(signature) => return Ok(signature),
                Err(e) => {
                    warn!("Swap attempt {} failed: {}", retries + 1, e);
                    last_error = Some(e);
                    retries += 1;

                    // Zwiększ slippage przy kolejnych próbach
                    // self.increase_slippage(); // TODO: implement slippage increase

                    // Czekaj przed kolejną próbą
                    tokio::time::sleep(tokio::time::Duration::from_millis(500 * retries as u64)).await;
                }
            }
        }

        Err(last_error.unwrap_or_else(|| anyhow::anyhow!("Swap failed after {} retries", max_retries)))
    }

    async fn execute_swap(
        &self,
        input_mint: &Pubkey,
        output_mint: &Pubkey,
        amount: u64,
        wallet: &Keypair,
    ) -> Result<String> {
        // Pobierz quote
        let quote_request = QuoteRequest {
            input_mint: input_mint.to_string(),
            output_mint: output_mint.to_string(),
            amount: amount.to_string(),
            slippage_bps: self.default_slippage_bps,
            only_direct_routes: Some(false),
            as_legacy_transaction: Some(false),
        };

        let quote = self.jupiter_client.get_quote(quote_request).await?;

        // Pobierz transakcję
        let swap_response = self.jupiter_client
            .get_swap_transaction(quote, &wallet.pubkey(), Some(100_000))
            .await?;

        // Deserializuj i podpisz transakcję
        let tx_bytes = base64::engine::general_purpose::STANDARD.decode(&swap_response.swap_transaction)?;
        let mut tx: Transaction = bincode::deserialize(&tx_bytes)?;

        // Podpisz transakcję
        tx.sign(&[wallet], tx.message.recent_blockhash);

        // W prawdziwej implementacji: wyślij przez RPC
        // let signature = rpc_client.send_and_confirm_transaction(&tx)?;

        Ok("simulated_signature".to_string())
    }

    fn increase_slippage(&mut self) {
        self.default_slippage_bps = (self.default_slippage_bps + 50).min(500);
        debug!("Increased slippage to {} bps", self.default_slippage_bps);
    }
}
