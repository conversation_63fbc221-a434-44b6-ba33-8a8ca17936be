use anyhow::Result;
use solana_client::rpc_client::RpcClient;
use solana_sdk::{
    pubkey::Pubkey,
    signature::Keypair,
    signer::Signer,
};
use std::sync::Arc;
use std::collections::HashMap;
use std::time::{Duration, Instant};
use tokio::sync::RwLock;
use tracing::{info, warn, error, debug};

use crate::config::Config;
use crate::memory::LocalMemory;
use crate::strategies::Strategy;
use crate::helius::HeliusClient;

/// Multi-provider RPC client with failover support
pub struct RpcManager {
    providers: Vec<RpcProvider>,
    current_index: Arc<RwLock<usize>>,
    health_status: Arc<RwLock<HashMap<String, ProviderHealth>>>,
}

#[derive(Clone)]
pub struct RpcProvider {
    pub name: String,
    pub client: Arc<RpcClient>,
    pub endpoint: String,
    pub priority: u8,
    pub is_premium: bool,
}

#[derive(Clone)]
pub struct ProviderHealth {
    pub is_healthy: bool,
    pub consecutive_failures: u32,
    pub last_check: Instant,
    pub avg_latency_ms: Option<u64>,
}

pub struct SniperBot {
    config: Config,
    memory: Arc<RwLock<LocalMemory>>,
    rpc_manager: Arc<RpcManager>,
    helius_client: Arc<HeliusClient>,
    wallet: Arc<Keypair>,
    is_running: Arc<RwLock<bool>>,
    start_time: Instant,
}

impl RpcManager {
    pub async fn new(endpoints: &[String]) -> Result<Self> {
        let mut providers = Vec::new();
        let mut health_status = HashMap::new();

        for (index, endpoint) in endpoints.iter().enumerate() {
            let client = Arc::new(RpcClient::new(endpoint));
            let is_premium = endpoint.contains("helius");

            let provider = RpcProvider {
                name: format!("provider_{}", index),
                client,
                endpoint: endpoint.clone(),
                priority: if is_premium { 10 } else { 5 },
                is_premium,
            };

            health_status.insert(
                endpoint.clone(),
                ProviderHealth {
                    is_healthy: true,
                    consecutive_failures: 0,
                    last_check: Instant::now(),
                    avg_latency_ms: None,
                },
            );

            providers.push(provider);
        }

        // Sort by priority (premium first)
        providers.sort_by(|a, b| b.priority.cmp(&a.priority));

        Ok(Self {
            providers,
            current_index: Arc::new(RwLock::new(0)),
            health_status: Arc::new(RwLock::new(health_status)),
        })
    }

    pub async fn get_best_client(&self) -> Result<Arc<RpcClient>> {
        let health = self.health_status.read().await;

        // Find first healthy provider
        for provider in &self.providers {
            if let Some(status) = health.get(&provider.endpoint) {
                if status.is_healthy {
                    return Ok(provider.client.clone());
                }
            }
        }

        // If no healthy providers, return first one and hope for the best
        warn!("No healthy RPC providers found, using fallback");
        Ok(self.providers[0].client.clone())
    }

    pub async fn execute_with_retry<T, F>(&self, operation: F) -> Result<T>
    where
        F: Fn(&RpcClient) -> std::pin::Pin<Box<dyn std::future::Future<Output = Result<T>> + Send>> + Send + Sync,
        T: Send,
    {
        let max_retries = 3;
        let mut last_error = None;

        for attempt in 0..max_retries {
            let client = self.get_best_client().await?;

            match operation(&client).await {
                Ok(result) => return Ok(result),
                Err(e) => {
                    last_error = Some(e);

                    // Mark provider as potentially unhealthy
                    self.mark_provider_error(&client).await;

                    // Exponential backoff
                    if attempt < max_retries - 1 {
                        tokio::time::sleep(Duration::from_millis(100 * (2_u64.pow(attempt)))).await;
                    }
                }
            }
        }

        Err(last_error.unwrap_or_else(|| anyhow::anyhow!("All retries failed")))
    }

    async fn mark_provider_error(&self, _client: &RpcClient) {
        // Implementation for marking provider as unhealthy
        // This is a simplified version - in production would need proper client comparison
        warn!("RPC provider error detected, implementing health check");
    }
}

impl SniperBot {
    pub async fn new(config: Config, memory: Arc<RwLock<LocalMemory>>) -> Result<Self> {
        // Initialize RPC manager with failover
        let rpc_manager = Arc::new(RpcManager::new(&config.rpc.endpoints).await?);

        // Initialize Helius client
        let helius_client = Arc::new(HeliusClient::new(
            config.rpc.helius_api_key.clone(),
            config.rpc.endpoints[0].clone(),
        )?);

        // Load wallet
        let wallet = Self::load_wallet(&config)?;
        let wallet_pubkey = wallet.pubkey();

        info!("🔑 Wallet: {}", wallet_pubkey);
        info!("🌐 RPC manager initialized with {} providers", config.rpc.endpoints.len());

        // Check balance using RPC manager
        let balance_result = rpc_manager.execute_with_retry(|client| {
            Box::pin(async move {
                client.get_balance(&wallet_pubkey)
                    .map_err(|e| anyhow::anyhow!("Balance check failed: {}", e))
            })
        }).await;

        match balance_result {
            Ok(balance) => {
                let sol_balance = balance as f64 / 1e9;
                info!("💰 Balance: {} SOL", sol_balance);

                if sol_balance < 0.1 {
                    warn!("⚠️ Low balance! Consider adding more SOL.");
                }
            }
            Err(e) => {
                error!("Failed to get balance: {}", e);
                if config.trading.dry_run {
                    info!("Running in dry-run mode, continuing...");
                } else {
                    return Err(anyhow::anyhow!("Cannot start with unknown balance"));
                }
            }
        }

        Ok(Self {
            config,
            memory,
            rpc_manager,
            helius_client,
            wallet: Arc::new(wallet),
            is_running: Arc::new(RwLock::new(false)),
            start_time: Instant::now(),
        })
    }

    pub async fn run(&self, strategy: Box<dyn Strategy>) -> Result<()> {
        *self.is_running.write().await = true;

        info!("🚀 Starting {} in {} mode",
            strategy.name(),
            if self.config.trading.dry_run { "DRY RUN" } else { "LIVE" }
        );

        // Main trading loop
        while *self.is_running.read().await && strategy.should_continue().await {
            match strategy.execute().await {
                Ok(()) => {
                    // Log metrics periodically
                    let metrics = strategy.get_metrics().await;
                    if metrics.total_trades % 10 == 0 && metrics.total_trades > 0 {
                        info!(
                            "📊 Stats - Trades: {}, Win Rate: {:.1}%, PnL: {:+.4} SOL, Balance: {:.4} SOL",
                            metrics.total_trades,
                            metrics.win_rate,
                            metrics.total_pnl_sol,
                            metrics.current_balance_sol
                        );
                    }
                }
                Err(e) => {
                    error!("Strategy error: {}", e);
                    tokio::time::sleep(tokio::time::Duration::from_secs(30)).await;
                }
            }

            // Check for shutdown signal
            if tokio::signal::ctrl_c().await.is_ok() {
                info!("🛑 Shutdown signal received");
                *self.is_running.write().await = false;
            }
        }

        // Final stats
        let final_metrics = strategy.get_metrics().await;
        info!("🏁 Final Results:");
        info!("   Total Trades: {}", final_metrics.total_trades);
        info!("   Win Rate: {:.1}%", final_metrics.win_rate);
        info!("   Total PnL: {:+.4} SOL", final_metrics.total_pnl_sol);
        info!("   Final Balance: {:.4} SOL", final_metrics.current_balance_sol);

        Ok(())
    }

    pub async fn stop(&self) {
        *self.is_running.write().await = false;
    }

    pub fn get_rpc_manager(&self) -> Arc<RpcManager> {
        self.rpc_manager.clone()
    }

    pub fn get_helius_client(&self) -> Arc<HeliusClient> {
        self.helius_client.clone()
    }

    pub fn get_wallet(&self) -> Arc<Keypair> {
        self.wallet.clone()
    }

    fn create_rpc_client(config: &Config) -> Result<Arc<RpcClient>> {
        // Try Helius endpoints first, then fallback
        for endpoint in &config.rpc.endpoints {
            match RpcClient::new(endpoint.clone()) {
                client => {
                    info!("🌐 Connected to RPC: {}",
                        if endpoint.contains("helius") { "Helius RPC" } else { "Public RPC" }
                    );
                    return Ok(Arc::new(client));
                }
            }
        }

        Err(anyhow::anyhow!("Failed to connect to any RPC endpoint"))
    }

    fn load_wallet(config: &Config) -> Result<Keypair> {
        if config.wallet.use_env_key {
            // Try to load from environment variable
            if let Ok(key) = std::env::var("SOLANA_PRIVATE_KEY") {
                let bytes = bs58::decode(key).into_vec()?;
                return Ok(Keypair::from_bytes(&bytes)?);
            }
        }

        // Load from file
        let wallet_path = &config.wallet.private_key_path;
        if std::path::Path::new(wallet_path).exists() {
            let wallet_json = std::fs::read_to_string(wallet_path)?;
            let wallet_bytes: Vec<u8> = serde_json::from_str(&wallet_json)?;
            Ok(Keypair::from_bytes(&wallet_bytes)?)
        } else {
            // Generate new wallet for testing
            warn!("⚠️ No wallet found, generating new one for testing");
            let new_wallet = Keypair::new();
            info!("📝 New wallet pubkey: {}", new_wallet.pubkey());
            Ok(new_wallet)
        }
    }
}
