version: '3.8'

services:
  sniperbot:
    build: .
    environment:
      - RUST_LOG=info
      - SOLANA_PRIVATE_KEY=${SOLANA_PRIVATE_KEY}
      - HELIUS_API_KEY=${HELIUS_API_KEY}
    volumes:
      - ./config.toml:/app/config.toml
      - ./data:/app/data
      - ./.keys:/app/.keys:ro
    ports:
      - "8080:8080"
    restart: unless-stopped
    
  # Optional: Local Qdrant for vector memory (lighter than full OpenMemory)
  qdrant:
    image: qdrant/qdrant:latest
    ports:
      - "6333:6333"
    volumes:
      - ./qdrant_storage:/qdrant/storage
    restart: unless-stopped
