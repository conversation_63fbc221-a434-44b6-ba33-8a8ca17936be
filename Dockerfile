FROM rust:1.75 as builder

WORKDIR /app
COPY Cargo.toml Cargo.lock ./
COPY src ./src

RUN cargo build --release

FROM debian:bookworm-slim

RUN apt-get update && apt-get install -y \
    ca-certificates \
    && rm -rf /var/lib/apt/lists/*

WORKDIR /app

COPY --from=builder /app/target/release/sniperbot /usr/local/bin/
COPY config.toml ./

# Create data directory
RUN mkdir -p /app/data

EXPOSE 8080

CMD ["sniperbot"]
