.PHONY: help build run test clean dev setup frontend docker logs stop check format lint docs

# Default target
help:
	@echo "SniperBot - Makefile Commands"
	@echo "============================="
	@echo "Setup & Build:"
	@echo "  make setup      - Install dependencies and setup environment"
	@echo "  make build      - Build the bot in release mode"
	@echo "  make clean      - Clean build artifacts"
	@echo ""
	@echo "Development:"
	@echo "  make dev        - Run bot in development mode"
	@echo "  make run        - Run bot in release mode"
	@echo "  make test       - Run all tests"
	@echo "  make check      - Run cargo check"
	@echo "  make format     - Format code with rustfmt"
	@echo "  make lint       - Run clippy linter"
	@echo ""
	@echo "Frontend:"
	@echo "  make frontend-setup - Setup frontend dependencies"
	@echo "  make frontend-dev   - Run frontend dev server"
	@echo "  make frontend-build - Build frontend for production"
	@echo ""
	@echo "Docker:"
	@echo "  make docker-build - Build Docker image"
	@echo "  make docker-run   - Run with docker-compose"
	@echo "  make docker-stop  - Stop docker containers"
	@echo "  make logs         - View docker logs"
	@echo ""
	@echo "Database:"
	@echo "  make db-reset    - Reset local database"
	@echo "  make db-migrate  - Run database migrations"
	@echo ""
	@echo "Testing:"
	@echo "  make test-unit   - Run unit tests only"
	@echo "  make test-int    - Run integration tests"
	@echo "  make test-e2e    - Run end-to-end tests"
	@echo "  make coverage    - Generate test coverage report"
	@echo ""
	@echo "Documentation:"
	@echo "  make docs        - Generate documentation"
	@echo "  make docs-serve  - Serve documentation locally"

# Variables
RUST_LOG ?= info
CARGO = cargo
NPM = npm
DOCKER_COMPOSE = docker-compose

# Setup
setup:
	@echo "🔧 Setting up SniperBot environment..."
	@rustup update stable
	@rustup component add rustfmt clippy
	@cargo install cargo-tarpaulin cargo-watch cargo-audit
	@mkdir -p .keys data/memory.db
	@echo "✅ Setup complete!"

# Build
build:
	@echo "🔨 Building SniperBot..."
	@$(CARGO) build --release
	@echo "✅ Build complete!"

clean:
	@echo "🧹 Cleaning build artifacts..."
	@$(CARGO) clean
	@rm -rf target/
	@rm -rf frontend/dist/
	@echo "✅ Clean complete!"

# Development
dev:
	@echo "🚀 Starting SniperBot in development mode..."
	@RUST_LOG=$(RUST_LOG) $(CARGO) run

run:
	@echo "🚀 Starting SniperBot in release mode..."
	@RUST_LOG=$(RUST_LOG) $(CARGO) run --release

watch:
	@echo "👁️  Starting development with auto-reload..."
	@RUST_LOG=$(RUST_LOG) cargo watch -x run

# Testing
test:
	@echo "🧪 Running all tests..."
	@$(CARGO) test --all-features

test-unit:
	@echo "🧪 Running unit tests..."
	@$(CARGO) test --lib

test-int:
	@echo "🧪 Running integration tests..."
	@$(CARGO) test --test '*'

test-e2e:
	@echo "🧪 Running E2E tests..."
	@./scripts/setup_test_env.sh
	@$(CARGO) test --features e2e

coverage:
	@echo "📊 Generating test coverage..."
	@cargo tarpaulin --out Html
	@echo "✅ Coverage report generated at tarpaulin-report.html"

# Code Quality
check:
	@echo "✓ Running cargo check..."
	@$(CARGO) check --all-features

format:
	@echo "🎨 Formatting code..."
	@$(CARGO) fmt

lint:
	@echo "🔍 Running clippy..."
	@$(CARGO) clippy -- -D warnings

audit:
	@echo "🔒 Running security audit..."
	@$(CARGO) audit

# Frontend
frontend-setup:
	@echo "📦 Setting up frontend..."
	@cd frontend && $(NPM) install
	@echo "✅ Frontend setup complete!"

frontend-dev:
	@echo "🎨 Starting frontend dev server..."
	@cd frontend && $(NPM) run dev

frontend-build:
	@echo "🏗️  Building frontend..."
	@cd frontend && $(NPM) run build
	@echo "✅ Frontend build complete!"

frontend-test:
	@echo "🧪 Running frontend tests..."
	@cd frontend && $(NPM) test

# Docker
docker-build:
	@echo "🐳 Building Docker image..."
	@docker build -t sniperbot:latest .

docker-run:
	@echo "🐳 Starting with docker-compose..."
	@$(DOCKER_COMPOSE) up -d

docker-stop:
	@echo "🛑 Stopping docker containers..."
	@$(DOCKER_COMPOSE) down

logs:
	@echo "📜 Showing docker logs..."
	@$(DOCKER_COMPOSE) logs -f sniperbot

# Database
db-reset:
	@echo "🗄️  Resetting database..."
	@rm -f data/memory.db
	@mkdir -p data
	@echo "✅ Database reset complete!"

db-migrate:
	@echo "🗄️  Running database migrations..."
	@sqlx migrate run
	@echo "✅ Migrations complete!"

# Documentation
docs:
	@echo "📚 Generating documentation..."
	@$(CARGO) doc --no-deps --open

docs-serve:
	@echo "📚 Serving documentation..."
	@python3 -m http.server 8000 -d target/doc

# Deployment helpers
deploy-devnet:
	@echo "🚀 Deploying to devnet..."
	@cp config.devnet.toml config.toml
	@make build
	@echo "✅ Ready for devnet testing!"

deploy-mainnet:
	@echo "⚠️  Preparing for mainnet deployment..."
	@echo "Please ensure:"
	@echo "  1. config.toml has dry_run = false"
	@echo "  2. Wallet has sufficient SOL"
	@echo "  3. All tests pass"
	@echo "  4. Security audit complete"
	@read -p "Continue? [y/N] " -n 1 -r; \
	if [[ $$REPLY =~ ^[Yy]$$ ]]; then \
		make build; \
		echo "✅ Mainnet build complete!"; \
	else \
		echo "❌ Deployment cancelled"; \
	fi

# Combined commands
all: clean build test

dev-all: format lint test dev

ci: format lint test audit

# Quick commands
b: build
r: run
t: test
f: format
l: lint
d: dev

# Create necessary directories
init-dirs:
	@mkdir -p .keys data logs frontend/src frontend/public

# Environment setup
env-example:
	@echo "Creating .env.example..."
	@echo "SOLANA_PRIVATE_KEY=" > .env.example
	@echo "HELIUS_API_KEY=40a78e4c-bdd0-4338-877a-aa7d56a5f5a0" >> .env.example
	@echo "RUST_LOG=info" >> .env.example
	@echo "✅ .env.example created!"

# Status check
status:
	@echo "📊 SniperBot Status Check"
	@echo "========================"
	@echo "Rust version: $$(rustc --version)"
	@echo "Cargo version: $$(cargo --version)"
	@echo "Node version: $$(node --version 2>/dev/null || echo 'Not installed')"
	@echo "NPM version: $$(npm --version 2>/dev/null || echo 'Not installed')"
	@echo ""
	@echo "Project files:"
	@ls -la config.toml 2>/dev/null || echo "❌ config.toml not found"
	@ls -la .env 2>/dev/null || echo "❌ .env not found"
	@ls -la .keys/wallet.json 2>/dev/null || echo "❌ wallet.json not found"
	@echo ""
	@echo "Dependencies:"
	@$(CARGO) tree --depth 1 | head -10

# Performance benchmarks
bench:
	@echo "📈 Running performance benchmarks..."
	@$(CARGO) bench

# Release build with optimizations
release:
	@echo "🚀 Building optimized release..."
	@RUSTFLAGS="-C target-cpu=native" $(CARGO) build --release
	@strip target/release/sniperbot
	@echo "✅ Optimized build complete!"
	@ls -lh target/release/sniperbot