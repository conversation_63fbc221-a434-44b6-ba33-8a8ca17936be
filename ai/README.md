# AI Components for SniperBot

This directory contains Python-based AI services that provide advanced analytics for the Rust-based sniper bot.

## Services

1. **Sentiment Analyzer** - Analyzes social media and news sentiment for tokens
2. **Risk Analyzer** - Evaluates token contract risks and rug pull probability  
3. **Price Predictor** - Predicts short-term price movements using ML
4. **Pattern Detector** - Identifies MEV patterns and trading opportunities

## Architecture

Each service runs as a separate microservice and communicates with the main bot via gRPC or REST API.

## Setup

```bash
cd ai
pip install -r requirements.txt
docker-compose up -d
```
