"""
Sentiment Analysis Service for Solana Tokens

This service analyzes sentiment from various sources:
- Twitter/X mentions
- Telegram/Discord channels
- Reddit discussions
- News articles
"""

from fastapi import FastAPI, HTTPException
from pydantic import BaseModel
from typing import List, Dict, Optional
from datetime import datetime
import asyncio
from loguru import logger
import numpy as np
from transformers import pipeline
import httpx
import os

app = FastAPI(title="Token Sentiment Analyzer", version="1.0.0")

# Initialize sentiment analyzer (using FinBERT for crypto context)
try:
    sentiment_pipeline = pipeline(
        "sentiment-analysis",
        model="ProsusAI/finbert",
        device=-1  # CPU for now, use 0 for GPU
    )
except Exception as e:
    logger.error(f"Failed to load sentiment model: {e}")
    sentiment_pipeline = None

class SentimentRequest(BaseModel):
    token_address: str
    token_symbol: str
    sources: Optional[List[str]] = ["twitter", "telegram", "reddit"]
    
class SentimentResponse(BaseModel):
    token_address: str
    token_symbol: str
    overall_sentiment: str  # bullish, bearish, neutral
    sentiment_score: float  # -1 to 1
    confidence: float
    sources_analyzed: int
    key_mentions: List[Dict[str, str]]
    timestamp: datetime

class SentimentAnalyzer:
    def __init__(self):
        self.brightdata_api_key = os.getenv("BRIGHTDATA_API_KEY", "")
        self.twitter_bearer_token = os.getenv("TWITTER_BEARER_TOKEN", "")
        
    async def analyze_token_sentiment(self, request: SentimentRequest) -> SentimentResponse:
        """Analyze sentiment for a specific token"""
        logger.info(f"Analyzing sentiment for {request.token_symbol}")
        
        # Collect data from various sources
        mentions = await self._collect_mentions(request)
        
        # Analyze sentiment
        sentiments = []
        key_mentions = []
        
        for mention in mentions[:100]:  # Limit to avoid overload
            if sentiment_pipeline:
                try:
                    result = sentiment_pipeline(mention['text'][:512])[0]
                    score = result['score'] if result['label'] == 'positive' else -result['score']
                    sentiments.append(score)
                    
                    # Track important mentions
                    if abs(score) > 0.7:
                        key_mentions.append({
                            'source': mention['source'],
                            'text': mention['text'][:200],
                            'sentiment': result['label'],
                            'score': score
                        })
                except Exception as e:
                    logger.error(f"Sentiment analysis error: {e}")
            else:
                # Fallback to simple keyword analysis
                score = self._simple_sentiment(mention['text'])
                sentiments.append(score)
        
        # Calculate overall sentiment
        if sentiments:
            avg_sentiment = np.mean(sentiments)
            confidence = 1 - np.std(sentiments) if len(sentiments) > 1 else 0.5
        else:
            avg_sentiment = 0.0
            confidence = 0.0
            
        # Determine sentiment category
        if avg_sentiment > 0.2:
            overall = "bullish"
        elif avg_sentiment < -0.2:
            overall = "bearish"
        else:
            overall = "neutral"
            
        return SentimentResponse(
            token_address=request.token_address,
            token_symbol=request.token_symbol,
            overall_sentiment=overall,
            sentiment_score=float(avg_sentiment),
            confidence=float(confidence),
            sources_analyzed=len(mentions),
            key_mentions=key_mentions[:5],  # Top 5 mentions
            timestamp=datetime.utcnow()
        )
    
    async def _collect_mentions(self, request: SentimentRequest) -> List[Dict]:
        """Collect mentions from various sources"""
        mentions = []
        
        # Twitter/X (via Brightdata or direct API)
        if "twitter" in request.sources:
            twitter_mentions = await self._fetch_twitter_mentions(request.token_symbol)
            mentions.extend(twitter_mentions)
            
        # Telegram (via scraping or APIs)
        if "telegram" in request.sources:
            # Simulate for now
            telegram_mentions = [
                {
                    'source': 'telegram',
                    'text': f'${request.token_symbol} is mooning! 🚀 Get in before it\'s too late!',
                    'timestamp': datetime.utcnow()
                },
                {
                    'source': 'telegram',
                    'text': f'Careful with ${request.token_symbol}, looks like a rug pull setup',
                    'timestamp': datetime.utcnow()
                }
            ]
            mentions.extend(telegram_mentions)
            
        # Reddit
        if "reddit" in request.sources:
            # Would integrate with Reddit API
            reddit_mentions = [
                {
                    'source': 'reddit',
                    'text': f'DD on ${request.token_symbol}: Strong fundamentals, active dev team',
                    'timestamp': datetime.utcnow()
                }
            ]
            mentions.extend(reddit_mentions)
            
        return mentions
    
    async def _fetch_twitter_mentions(self, symbol: str) -> List[Dict]:
        """Fetch Twitter mentions using Brightdata or Twitter API"""
        mentions = []
        
        if self.brightdata_api_key:
            # Use Brightdata for scraping
            try:
                # Brightdata API call would go here
                pass
            except Exception as e:
                logger.error(f"Brightdata error: {e}")
                
        elif self.twitter_bearer_token:
            # Use Twitter API v2
            try:
                async with httpx.AsyncClient() as client:
                    headers = {"Authorization": f"Bearer {self.twitter_bearer_token}"}
                    params = {
                        "query": f"${symbol} OR #{symbol}",
                        "max_results": 100,
                        "tweet.fields": "created_at,author_id"
                    }
                    response = await client.get(
                        "https://api.twitter.com/2/tweets/search/recent",
                        headers=headers,
                        params=params
                    )
                    if response.status_code == 200:
                        data = response.json()
                        for tweet in data.get('data', []):
                            mentions.append({
                                'source': 'twitter',
                                'text': tweet['text'],
                                'timestamp': datetime.fromisoformat(tweet['created_at'].replace('Z', '+00:00'))
                            })
            except Exception as e:
                logger.error(f"Twitter API error: {e}")
                
        # Fallback to simulated data
        if not mentions:
            mentions = [
                {
                    'source': 'twitter',
                    'text': f'${symbol} looking bullish! Dev team just announced major partnership',
                    'timestamp': datetime.utcnow()
                },
                {
                    'source': 'twitter',
                    'text': f'${symbol} chart forming a cup and handle, breakout imminent',
                    'timestamp': datetime.utcnow()
                }
            ]
            
        return mentions
    
    def _simple_sentiment(self, text: str) -> float:
        """Simple keyword-based sentiment analysis fallback"""
        positive_words = ['moon', 'bullish', 'pump', 'buy', 'gem', 'undervalued', 'potential']
        negative_words = ['rug', 'dump', 'scam', 'bearish', 'sell', 'overvalued', 'warning']
        
        text_lower = text.lower()
        positive_count = sum(1 for word in positive_words if word in text_lower)
        negative_count = sum(1 for word in negative_words if word in text_lower)
        
        if positive_count + negative_count == 0:
            return 0.0
            
        return (positive_count - negative_count) / (positive_count + negative_count)

# Initialize analyzer
analyzer = SentimentAnalyzer()

@app.post("/analyze", response_model=SentimentResponse)
async def analyze_sentiment(request: SentimentRequest):
    """Analyze sentiment for a token"""
    try:
        result = await analyzer.analyze_token_sentiment(request)
        return result
    except Exception as e:
        logger.error(f"Analysis error: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "model_loaded": sentiment_pipeline is not None,
        "timestamp": datetime.utcnow()
    }

@app.get("/")
async def root():
    """API information"""
    return {
        "service": "Token Sentiment Analyzer",
        "version": "1.0.0",
        "endpoints": ["/analyze", "/health"]
    }

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8001)
